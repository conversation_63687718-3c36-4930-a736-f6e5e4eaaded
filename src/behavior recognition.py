

import requests
import json
from typing import Dict,List,Optional
from datetime import datetime,timedelta
from flask_migrate import current
import pygeohash as pgh
import numpy as np
import hashlib
import os
import pickle
import sqlite3
try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False
    print("FAISS 不可用，将使用基础向量数据库")
try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    print("sentence-transformers 不可用，将使用简单哈希向量化方法")

try:
    from transformers import AutoTokenizer, AutoModel
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    print("transformers 不可用")

try:
    from llama_cpp import Llama
    LLAMA_CPP_AVAILABLE = True
except ImportError:
    LLAMA_CPP_AVAILABLE = False
    print("llama-cpp-python 不可用")

#API配置
API_BASE_URL = "http://**************:23071"
API_ENDPOINT = "/api/v1/ship/active/trend"

def fetch_ship_data(ship_id:str,start_time:str,end_time:str) -> Optional[Dict]:
    """
    从API获取船舶历史数据
    Args:
        ship_id (str): 船舶ID
        start_time (str): 开始时间
        end_time (str): 结束时间
    Returns:
        Dict: 历史数据字典或None
    """
    try:
        #构建完整API URL
        url = f"{API_BASE_URL}{API_ENDPOINT}"
        #准备请求参数
        params ={
            "ship_id":ship_id,
            "start_time":start_time,
            "end_time":end_time
        }
        proxies = {
            'http': None,
            'https': None
        }
        #发送GET请求
        response = requests.get(url,params=params,timeout=30,proxies=proxies)

        if response.status_code == 200:
            #解析JSON数据
            data = response.json()
            # 第一步：数据解析和筛选
            trace_data = data.get("trace_data", [])
            candidate_sets = ["编队", "航渡", "停靠", "巡航"]

            filtered_data = []
            for item in trace_data:
                if item["action"] in candidate_sets:
                    filtered_data.append(item)

            return filtered_data

        else:
            return None

    except requests.exceptions.Timeout:
        return None
    except requests.exceptions.ConnectionError:
        return None
    except json.JSONDecodeError:
        return None
    except Exception as e:
        return None
def segment_by_action(filtered_data):
    """
    第二步：按action进行事件分块
    将连续相同的action合并为事件区块
    """
    if not filtered_data:
        return []
    #找出所有区块的起始位置
    block_starts=[]
    for i in range(len(filtered_data)):
        current_action=filtered_data[i]["action"]
        if i==0 or filtered_data[i-1]["action"]!=current_action:
            block_starts.append(i)
    blocks=[]
    for i in range(len(block_starts)):
        start_pos=block_starts[i]
        #计算结束位置
        if i==len(block_starts)-1:
            end_pos = len(filtered_data)-1
        else:
            end_pos=block_starts[i+1]-1
        #创建区块信息
        block={
            'block_id':i+1,
            'action':filtered_data[start_pos]['action'],
            'start_pos':start_pos,
            'end_pos':end_pos,
            'size':end_pos-start_pos+1,
            'data_points':filtered_data[start_pos:end_pos+1]  #包含该区块的所有数据点
        }
        blocks.append(block)
    return blocks
def parse_time(time_str):
    """
    将时间字符串转换为datetime对象

    """
    return datetime.strptime(time_str,"%Y-%m-%d %H:%M:%S")


def extract_window_coordinates(block, window_start, window_end):
    """
    从区块中提取指定时间窗口内的坐标数据点
    Args:
        block: 事件区块
        window_start: 窗口开始时间 (datetime对象)
        window_end: 窗口结束时间 (datetime对象)
    Returns:
        List: 窗口内的坐标数据点列表
    """
    window_points = []

    for point in block['data_points']:
        point_time = parse_time(point['scout_time'])

        # 检查数据点是否在时间窗口内
        if window_start <= point_time <= window_end:
            window_points.append({
                'lat': point['lat'],
                'lng': point['lng'],
                'scout_time': point['scout_time']
            })

    return window_points


def coordinates_to_geohash(coordinates, precision=7):
    """
    第四步：坐标文本化 (Coordinate Textualization)
    将坐标序列转换为Geohash字符串序列
    Args:
        coordinates: 坐标点列表，每个点包含lat和lng
        precision: Geohash精度 (6-8位，默认7位)
    Returns:
        str: Geohash字符串序列，用空格分隔
    """
    if not coordinates:
        return ""

    geohash_list = []
    for coord in coordinates:
        # 使用pygeohash库将经纬度转换为geohash
        geohash = pgh.encode(coord['lat'], coord['lng'], precision=precision)
        geohash_list.append(geohash)

    # 将geohash序列用空格连接成字符串
    geohash_sentence = " ".join(geohash_list)
    return geohash_sentence


# 全局变量：缓存向量化模型
_embedding_model = None
_model_type = "qwen"  # 可选: "qwen", "sentence_transformer", "hash"

class VectorDatabase:
    """向量数据库类，用于存储和检索航迹行为向量"""

    def __init__(self, db_path: str = "vector_database"):
        self.db_path = db_path
        self.db_file = f"{db_path}.db"
        self.vectors_file = f"{db_path}_vectors.pkl"
        self.metadata_file = f"{db_path}_metadata.json"

        # 创建数据库目录
        os.makedirs(os.path.dirname(self.db_file) if os.path.dirname(self.db_file) else ".", exist_ok=True)

        # 初始化数据库
        self._init_database()

    def _init_database(self):
        """初始化SQLite数据库"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        # 创建向量元数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS vector_metadata (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ship_name TEXT NOT NULL,
                action TEXT NOT NULL,
                identity_text TEXT,
                geohash_sentence TEXT,
                coordinate_count INTEGER,
                vector_dim INTEGER,
                vector_norm REAL,
                time_range TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                vector_id INTEGER UNIQUE
            )
        ''')

        # 创建行为统计表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS behavior_stats (
                action TEXT PRIMARY KEY,
                total_samples INTEGER,
                avg_vector_dim REAL,
                avg_coordinate_count REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_action ON vector_metadata(action)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_ship_name ON vector_metadata(ship_name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_vector_dim ON vector_metadata(vector_dim)')

        conn.commit()
        conn.close()

    def build_from_windows_data(self, windows_data: List[Dict], ship_name: str):
        """
        从时间窗口数据构建向量数据库
        Args:
            windows_data: 阶段一生成的时间窗口数据
            ship_name: 舰船名称
        """
        vectors = []
        metadata_list = []
        behavior_stats = {}

        valid_count = 0

        for i, window in enumerate(windows_data):
            if window['vector'] is not None:
                vector = window['vector']

                # 确保向量是一维的
                if vector.ndim > 1:
                    vector = vector.flatten()

                # 计算向量范数
                vector_norm = float(np.linalg.norm(vector))

                # 存储向量
                vectors.append(vector)

                # 存储元数据
                metadata = {
                    'vector_id': valid_count,
                    'ship_name': ship_name,
                    'action': window['action'],
                    'identity_text': window['identity_text'],
                    'geohash_sentence': window['geohash_sentence'],
                    'coordinate_count': window['coordinate_count'],
                    'vector_dim': len(vector),
                    'vector_norm': vector_norm,
                    'time_range': window.get('time_range', 'Unknown')
                }
                metadata_list.append(metadata)

                # 统计行为信息
                action = window['action']
                if action not in behavior_stats:
                    behavior_stats[action] = {
                        'count': 0,
                        'total_dim': 0,
                        'total_coords': 0
                    }

                behavior_stats[action]['count'] += 1
                behavior_stats[action]['total_dim'] += len(vector)
                behavior_stats[action]['total_coords'] += window['coordinate_count']

                valid_count += 1

        # 保存向量数据
        self._save_vectors(vectors)

        # 保存元数据到数据库
        self._save_metadata_to_db(metadata_list)

        # 保存行为统计
        self._save_behavior_stats(behavior_stats)

        # 保存元数据到JSON文件（备份）
        self._save_metadata_to_json(metadata_list, behavior_stats)

        return len(vectors)

    def _save_vectors(self, vectors: List[np.ndarray]):
        """保存向量数据到pickle文件"""
        with open(self.vectors_file, 'wb') as f:
            pickle.dump(vectors, f)

    def _save_metadata_to_db(self, metadata_list: List[Dict]):
        """保存元数据到SQLite数据库"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        # 清空现有数据
        cursor.execute('DELETE FROM vector_metadata')

        # 插入新数据
        for metadata in metadata_list:
            cursor.execute('''
                INSERT INTO vector_metadata
                (ship_name, action, identity_text, geohash_sentence, coordinate_count,
                 vector_dim, vector_norm, time_range, vector_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                metadata['ship_name'],
                metadata['action'],
                metadata['identity_text'],
                metadata['geohash_sentence'],
                metadata['coordinate_count'],
                metadata['vector_dim'],
                metadata['vector_norm'],
                metadata['time_range'],
                metadata['vector_id']
            ))

        conn.commit()
        conn.close()

    def _save_behavior_stats(self, behavior_stats: Dict):
        """保存行为统计信息"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        # 清空现有统计
        cursor.execute('DELETE FROM behavior_stats')

        # 插入新统计
        for action, stats in behavior_stats.items():
            avg_dim = stats['total_dim'] / stats['count']
            avg_coords = stats['total_coords'] / stats['count']

            cursor.execute('''
                INSERT INTO behavior_stats (action, total_samples, avg_vector_dim, avg_coordinate_count)
                VALUES (?, ?, ?, ?)
            ''', (action, stats['count'], avg_dim, avg_coords))

        conn.commit()
        conn.close()

    def _save_metadata_to_json(self, metadata_list: List[Dict], behavior_stats: Dict):
        """保存元数据到JSON文件（备份）"""
        data = {
            'created_at': datetime.now().isoformat(),
            'total_vectors': len(metadata_list),
            'behavior_stats': {
                action: {
                    'total_samples': stats['count'],
                    'avg_vector_dim': stats['total_dim'] / stats['count'],
                    'avg_coordinate_count': stats['total_coords'] / stats['count']
                }
                for action, stats in behavior_stats.items()
            },
            'metadata': metadata_list
        }

        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

    def load_vectors(self) -> List[np.ndarray]:
        """加载向量数据"""
        if not os.path.exists(self.vectors_file):
            raise FileNotFoundError(f"向量文件不存在: {self.vectors_file}")

        with open(self.vectors_file, 'rb') as f:
            return pickle.load(f)

    def query_metadata(self, action: Optional[str] = None,
                      ship_name: Optional[str] = None,
                      min_vector_dim: Optional[int] = None,
                      max_vector_dim: Optional[int] = None) -> List[Dict]:
        """查询元数据"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        query = "SELECT * FROM vector_metadata WHERE 1=1"
        params = []

        if action:
            query += " AND action = ?"
            params.append(action)

        if ship_name:
            query += " AND ship_name = ?"
            params.append(ship_name)

        if min_vector_dim:
            query += " AND vector_dim >= ?"
            params.append(min_vector_dim)

        if max_vector_dim:
            query += " AND vector_dim <= ?"
            params.append(max_vector_dim)

        cursor.execute(query, params)
        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        conn.close()
        return results

    def get_behavior_stats(self) -> Dict:
        """获取行为统计信息"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM behavior_stats")
        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        conn.close()
        return {row['action']: row for row in results}

    def cosine_similarity_db(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """计算余弦相似度"""
        # 处理不同维度的向量
        min_dim = min(len(vec1), len(vec2))
        v1 = vec1[:min_dim]
        v2 = vec2[:min_dim]

        # 计算余弦相似度
        dot_product = np.dot(v1, v2)
        norm1 = np.linalg.norm(v1)
        norm2 = np.linalg.norm(v2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)

    def find_similar_vectors(self, query_vector: np.ndarray,
                           top_k: int = 5,
                           action_filter: Optional[str] = None,
                           similarity_threshold: float = 0.0):
        """
        查找相似向量
        Args:
            query_vector: 查询向量
            top_k: 返回最相似的前k个结果
            action_filter: 行为类型过滤
            similarity_threshold: 相似度阈值
        Returns:
            List of (vector_id, similarity, metadata)
        """
        # 加载向量和元数据
        vectors = self.load_vectors()
        metadata_list = self.query_metadata(action=action_filter)

        # 计算相似度
        similarities = []

        for metadata in metadata_list:
            vector_id = metadata['vector_id']
            if vector_id < len(vectors):
                similarity = self.cosine_similarity_db(query_vector, vectors[vector_id])

                if similarity >= similarity_threshold:
                    similarities.append((vector_id, similarity, metadata))

        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)

        return similarities[:top_k]

    def get_database_info(self) -> Dict:
        """获取数据库信息"""
        info = {
            'database_files': {
                'db_file': self.db_file,
                'vectors_file': self.vectors_file,
                'metadata_file': self.metadata_file
            },
            'file_exists': {
                'db_file': os.path.exists(self.db_file),
                'vectors_file': os.path.exists(self.vectors_file),
                'metadata_file': os.path.exists(self.metadata_file)
            }
        }

        if info['file_exists']['db_file']:
            # 获取统计信息
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            cursor.execute("SELECT COUNT(*) FROM vector_metadata")
            info['total_vectors'] = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(DISTINCT action) FROM vector_metadata")
            info['unique_actions'] = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(DISTINCT ship_name) FROM vector_metadata")
            info['unique_ships'] = cursor.fetchone()[0]

            conn.close()

        if info['file_exists']['vectors_file']:
            vectors = self.load_vectors()
            info['vector_file_size'] = len(vectors)
            if vectors:
                info['sample_vector_dim'] = len(vectors[0])

        return info

class FAISSVectorDatabase:
    """基于 FAISS 的高性能向量数据库"""

    def __init__(self, db_path: str = "faiss_vector_database"):
        self.db_path = db_path
        self.db_file = f"{db_path}.db"
        self.faiss_index_file = f"{db_path}_faiss.index"
        self.metadata_file = f"{db_path}_metadata.json"

        # FAISS 相关
        self.faiss_index = None
        self.vector_dimension = None
        self.index_type = "IVF"  # 可选: "Flat", "IVF", "HNSW"

        # 创建数据库目录
        os.makedirs(os.path.dirname(self.db_file) if os.path.dirname(self.db_file) else ".", exist_ok=True)

        # 初始化数据库
        self._init_database()

    def _init_database(self):
        """初始化SQLite数据库（存储元数据）"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        # 创建向量元数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS vector_metadata (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                faiss_id INTEGER UNIQUE,
                ship_name TEXT NOT NULL,
                action TEXT NOT NULL,
                identity_text TEXT,
                geohash_sentence TEXT,
                coordinate_count INTEGER,
                vector_dim INTEGER,
                vector_norm REAL,
                time_range TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建行为统计表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS behavior_stats (
                action TEXT PRIMARY KEY,
                total_samples INTEGER,
                avg_vector_dim REAL,
                avg_coordinate_count REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_faiss_id ON vector_metadata(faiss_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_action ON vector_metadata(action)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_ship_name ON vector_metadata(ship_name)')

        conn.commit()
        conn.close()

    def _create_faiss_index(self, vectors: List[np.ndarray], index_type: str = "IVF"):
        """创建 FAISS 索引"""
        if not FAISS_AVAILABLE:
            raise ImportError("FAISS 不可用，请安装: pip install faiss-cpu")

        # 标准化向量维度
        max_dim = max(len(v) for v in vectors)
        normalized_vectors = []

        for vector in vectors:
            if len(vector) < max_dim:
                # 零填充
                padded = np.zeros(max_dim, dtype=np.float32)
                padded[:len(vector)] = vector.astype(np.float32)
                normalized_vectors.append(padded)
            else:
                # 截断
                normalized_vectors.append(vector[:max_dim].astype(np.float32))

        vectors_matrix = np.array(normalized_vectors)
        self.vector_dimension = max_dim

        if index_type == "Flat":
            # 精确搜索，适合小数据集
            index = faiss.IndexFlatIP(max_dim)  # 内积相似度
        elif index_type == "IVF":
            # 倒排文件索引，适合中等数据集
            nlist = min(100, len(vectors) // 10)  # 聚类中心数
            quantizer = faiss.IndexFlatIP(max_dim)
            index = faiss.IndexIVFFlat(quantizer, max_dim, nlist)

            # 训练索引
            index.train(vectors_matrix)
        elif index_type == "HNSW":
            # 分层导航小世界图，适合大数据集
            index = faiss.IndexHNSWFlat(max_dim, 32)
        else:
            raise ValueError(f"不支持的索引类型: {index_type}")

        # 添加向量到索引
        index.add(vectors_matrix)

        self.faiss_index = index
        return index

    def build_from_windows_data(self, windows_data: List[Dict], ship_name: str, index_type: str = "IVF"):
        """
        从时间窗口数据构建 FAISS 向量数据库
        Args:
            windows_data: 阶段一生成的时间窗口数据
            ship_name: 舰船名称
            index_type: FAISS索引类型 ("Flat", "IVF", "HNSW")
        """
        print(f"🔄 第六步：构建 {ship_name} 的 FAISS 向量数据库...")

        vectors = []
        metadata_list = []
        behavior_stats = {}

        valid_count = 0

        for i, window in enumerate(windows_data):
            if window['vector'] is not None:
                vector = window['vector']

                # 确保向量是一维的
                if vector.ndim > 1:
                    vector = vector.flatten()

                # 计算向量范数
                vector_norm = float(np.linalg.norm(vector))

                # 存储向量
                vectors.append(vector)

                # 存储元数据
                metadata = {
                    'faiss_id': valid_count,
                    'ship_name': ship_name,
                    'action': window['action'],
                    'identity_text': window['identity_text'],
                    'geohash_sentence': window['geohash_sentence'],
                    'coordinate_count': window['coordinate_count'],
                    'vector_dim': len(vector),
                    'vector_norm': vector_norm,
                    'time_range': window.get('time_range', 'Unknown')
                }
                metadata_list.append(metadata)

                # 统计行为信息
                action = window['action']
                if action not in behavior_stats:
                    behavior_stats[action] = {
                        'count': 0,
                        'total_dim': 0,
                        'total_coords': 0
                    }

                behavior_stats[action]['count'] += 1
                behavior_stats[action]['total_dim'] += len(vector)
                behavior_stats[action]['total_coords'] += window['coordinate_count']

                valid_count += 1

        # 创建 FAISS 索引
        self.index_type = index_type
        self._create_faiss_index(vectors, index_type)

        # 保存 FAISS 索引
        self._save_faiss_index()

        # 保存元数据到数据库
        self._save_metadata_to_db(metadata_list)

        # 保存行为统计
        self._save_behavior_stats(behavior_stats)

        # 保存元数据到JSON文件（备份）
        self._save_metadata_to_json(metadata_list, behavior_stats)

        return len(vectors)

    def _save_faiss_index(self):
        """保存 FAISS 索引到文件"""
        if self.faiss_index is not None:
            faiss.write_index(self.faiss_index, self.faiss_index_file)

    def _load_faiss_index(self):
        """从文件加载 FAISS 索引"""
        if os.path.exists(self.faiss_index_file):
            self.faiss_index = faiss.read_index(self.faiss_index_file)
            self.vector_dimension = self.faiss_index.d
            return True
        return False

    def _save_metadata_to_db(self, metadata_list: List[Dict]):
        """保存元数据到SQLite数据库"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        # 清空现有数据
        cursor.execute('DELETE FROM vector_metadata')

        # 插入新数据
        for metadata in metadata_list:
            cursor.execute('''
                INSERT INTO vector_metadata
                (faiss_id, ship_name, action, identity_text, geohash_sentence, coordinate_count,
                 vector_dim, vector_norm, time_range)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                metadata['faiss_id'],
                metadata['ship_name'],
                metadata['action'],
                metadata['identity_text'],
                metadata['geohash_sentence'],
                metadata['coordinate_count'],
                metadata['vector_dim'],
                metadata['vector_norm'],
                metadata['time_range']
            ))

        conn.commit()
        conn.close()

    def _save_behavior_stats(self, behavior_stats: Dict):
        """保存行为统计信息"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        # 清空现有统计
        cursor.execute('DELETE FROM behavior_stats')

        # 插入新统计
        for action, stats in behavior_stats.items():
            avg_dim = stats['total_dim'] / stats['count']
            avg_coords = stats['total_coords'] / stats['count']

            cursor.execute('''
                INSERT INTO behavior_stats (action, total_samples, avg_vector_dim, avg_coordinate_count)
                VALUES (?, ?, ?, ?)
            ''', (action, stats['count'], avg_dim, avg_coords))

        conn.commit()
        conn.close()

    def _save_metadata_to_json(self, metadata_list: List[Dict], behavior_stats: Dict):
        """保存元数据到JSON文件（备份）"""
        data = {
            'created_at': datetime.now().isoformat(),
            'total_vectors': len(metadata_list),
            'vector_dimension': self.vector_dimension,
            'index_type': self.index_type,
            'faiss_available': FAISS_AVAILABLE,
            'behavior_stats': {
                action: {
                    'total_samples': stats['count'],
                    'avg_vector_dim': stats['total_dim'] / stats['count'],
                    'avg_coordinate_count': stats['total_coords'] / stats['count']
                }
                for action, stats in behavior_stats.items()
            },
            'metadata': metadata_list
        }

        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

    def search_similar_vectors(self, query_vector: np.ndarray,
                             top_k: int = 5,
                             action_filter: Optional[str] = None):
        """
        使用 FAISS 进行高效相似度搜索
        Args:
            query_vector: 查询向量
            top_k: 返回最相似的前k个结果
            action_filter: 行为类型过滤
        Returns:
            List of (faiss_id, similarity_score, metadata)
        """
        if self.faiss_index is None:
            if not self._load_faiss_index():
                raise ValueError("FAISS 索引未加载")

        # 标准化查询向量
        if len(query_vector) < self.vector_dimension:
            padded_query = np.zeros(self.vector_dimension, dtype=np.float32)
            padded_query[:len(query_vector)] = query_vector.astype(np.float32)
        else:
            padded_query = query_vector[:self.vector_dimension].astype(np.float32)

        # 重塑为二维数组
        query_matrix = padded_query.reshape(1, -1)

        # FAISS 搜索
        if self.index_type == "IVF":
            # 设置搜索参数
            self.faiss_index.nprobe = min(10, self.faiss_index.nlist)

        scores, indices = self.faiss_index.search(query_matrix, top_k * 2)  # 多搜索一些以便过滤

        # 获取元数据
        results = []
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        for i, (score, faiss_id) in enumerate(zip(scores[0], indices[0])):
            if faiss_id == -1:  # FAISS 返回 -1 表示无效结果
                continue

            # 查询元数据
            cursor.execute("SELECT * FROM vector_metadata WHERE faiss_id = ?", (int(faiss_id),))
            row = cursor.fetchone()

            if row:
                columns = [description[0] for description in cursor.description]
                metadata = dict(zip(columns, row))

                # 应用行为过滤
                if action_filter is None or metadata['action'] == action_filter:
                    results.append((int(faiss_id), float(score), metadata))

                    if len(results) >= top_k:
                        break

        conn.close()
        return results

    def get_database_info(self) -> Dict:
        """获取数据库信息"""
        info = {
            'database_files': {
                'db_file': self.db_file,
                'faiss_index_file': self.faiss_index_file,
                'metadata_file': self.metadata_file
            },
            'file_exists': {
                'db_file': os.path.exists(self.db_file),
                'faiss_index_file': os.path.exists(self.faiss_index_file),
                'metadata_file': os.path.exists(self.metadata_file)
            },
            'faiss_available': FAISS_AVAILABLE,
            'vector_dimension': self.vector_dimension,
            'index_type': self.index_type
        }

        if info['file_exists']['db_file']:
            # 获取统计信息
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            cursor.execute("SELECT COUNT(*) FROM vector_metadata")
            info['total_vectors'] = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(DISTINCT action) FROM vector_metadata")
            info['unique_actions'] = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(DISTINCT ship_name) FROM vector_metadata")
            info['unique_ships'] = cursor.fetchone()[0]

            conn.close()

        return info

def step6_build_vector_database(all_windows, ship_name, db_name=None, use_faiss=True):
    """
    第六步：构建并存储向量数据库
    Args:
        all_windows: 前五步生成的时间窗口数据
        ship_name: 舰船名称
        db_name: 数据库名称（可选）
        use_faiss: 是否使用 FAISS 向量数据库
    Returns:
        VectorDatabase 或 FAISSVectorDatabase 实例
    """
    print(f"\n=== 第六步：构建并存储向量数据库 ===")

    # 设置数据库名称
    if not db_name:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if use_faiss and FAISS_AVAILABLE:
            db_name = f"databases/faiss_vector_db_{ship_name}_{timestamp}"
        else:
            db_name = f"databases/vector_db_{ship_name}_{timestamp}"

    # 创建向量数据库实例
    if use_faiss and FAISS_AVAILABLE:
        print("🚀 使用 FAISS 高性能向量数据库")
        vector_db = FAISSVectorDatabase(db_name)
        # 构建 FAISS 向量数据库
        vector_count = vector_db.build_from_windows_data(all_windows, ship_name, index_type="IVF")
    else:
        if use_faiss:
            print("⚠️ FAISS 不可用，回退到基础向量数据库")
        else:
            print("📦 使用基础向量数据库")
        vector_db = VectorDatabase(db_name)
        # 构建基础向量数据库
        vector_count = vector_db.build_from_windows_data(all_windows, ship_name)

    # 显示数据库信息
    db_info = vector_db.get_database_info()

    if hasattr(vector_db, 'get_behavior_stats'):
        behavior_stats = vector_db.get_behavior_stats()
    else:
        behavior_stats = {}

    print(f"\n📊 向量数据库统计:")
    print(f"  - 数据库类型: {'FAISS' if isinstance(vector_db, FAISSVectorDatabase) else '基础'}")
    print(f"  - 总向量数: {db_info.get('total_vectors', 0)}")
    print(f"  - 向量维度: {db_info.get('vector_dimension', 'Unknown')}")
    print(f"  - 索引类型: {db_info.get('index_type', 'N/A')}")
    print(f"  - 行为类型数: {db_info.get('unique_actions', 0)}")
    print(f"  - 舰船数: {db_info.get('unique_ships', 0)}")

    if behavior_stats:
        print(f"\n📈 各行为类型统计:")
        for action, stats in behavior_stats.items():
            print(f"  {action}:")
            print(f"    样本数: {stats['total_samples']}")
            print(f"    平均向量维度: {stats['avg_vector_dim']:.1f}")
            print(f"    平均坐标点数: {stats['avg_coordinate_count']:.1f}")

    print(f"\n💾 数据库文件:")
    for file_type, file_path in db_info['database_files'].items():
        exists = "✅" if db_info['file_exists'][file_type] else "❌"
        print(f"  {exists} {file_type}: {file_path}")

    return vector_db

def demo_vector_database_query(vector_db):
    """演示向量数据库查询功能"""
    print(f"\n=== 演示向量数据库查询功能 ===")

    try:
        is_faiss = isinstance(vector_db, FAISSVectorDatabase)

        if hasattr(vector_db, 'query_metadata'):
            # 查询停靠行为的向量
            docking_metadata = vector_db.query_metadata(action="停靠")
            print(f"📍 停靠行为向量数: {len(docking_metadata)}")

            # 查询巡航行为的向量
            cruise_metadata = vector_db.query_metadata(action="巡航")
            print(f"🚢 巡航行为向量数: {len(cruise_metadata)}")

            # 查询航渡行为的向量
            transit_metadata = vector_db.query_metadata(action="航渡")
            print(f"🛤️ 航渡行为向量数: {len(transit_metadata)}")

            # 查询高维向量
            high_dim_metadata = vector_db.query_metadata(min_vector_dim=10000)
            print(f"📏 高维向量(>=10000维)数: {len(high_dim_metadata)}")
        else:
            print("⚠️ 当前数据库不支持元数据查询")
            docking_metadata = []

        # 演示相似度搜索
        if docking_metadata:
            print(f"\n🔍 演示{'FAISS' if is_faiss else '基础'}相似度搜索:")

            if is_faiss:
                # FAISS 搜索
                # 创建一个模拟查询向量（使用第一个停靠行为的特征）
                identity_text = docking_metadata[0]['identity_text']
                query_vector = text_to_vector(identity_text)

                # 使用 FAISS 搜索
                similar_vectors = vector_db.search_similar_vectors(
                    query_vector,
                    top_k=3,
                    action_filter="停靠"
                )

                print(f"  查询文本: {identity_text}")
                print(f"  FAISS 找到 {len(similar_vectors)} 个相似向量:")
                for i, (faiss_id, score, metadata) in enumerate(similar_vectors, 1):
                    print(f"    {i}. FAISS_ID:{faiss_id}, 得分:{score:.4f}, 行为:{metadata['action']}")
            else:
                # 基础搜索
                vectors = vector_db.load_vectors()
                query_vector_id = docking_metadata[0]['vector_id']
                query_vector = vectors[query_vector_id]

                similar_vectors = vector_db.find_similar_vectors(
                    query_vector,
                    top_k=3,
                    action_filter="停靠",
                    similarity_threshold=0.5
                )

                print(f"  查询向量: 停靠行为 (ID: {query_vector_id})")
                print(f"  基础搜索找到 {len(similar_vectors)} 个相似向量:")
                for i, (vector_id, similarity, metadata) in enumerate(similar_vectors, 1):
                    print(f"    {i}. ID:{vector_id}, 相似度:{similarity:.4f}, 维度:{metadata['vector_dim']}")

    except Exception as e:
        print(f"❌ 查询演示失败: {e}")
        import traceback
        traceback.print_exc()

# 此函数已被阶段二的 OnlineInferenceEngine 替代，已删除

# ==================== 阶段二：在线查询 (Online Inference) ====================

class OnlineInferenceEngine:
    """在线推理引擎，用于实时航迹行为分类"""

    def __init__(self):
        self.vector_databases = {}  # 存储不同舰船的向量数据库
        self.current_ship_name = None

    def load_or_build_ship_database(self, ship_name: str, force_rebuild: bool = False):
        """
        加载或构建指定舰船的向量数据库
        Args:
            ship_name: 舰船名称
            force_rebuild: 是否强制重建数据库
        Returns:
            向量数据库实例
        """
        # 检查是否已经有该舰船的数据库
        if ship_name in self.vector_databases and not force_rebuild:
            return self.vector_databases[ship_name]

        # 尝试加载已存在的数据库文件
        if not force_rebuild:
            existing_db = self._try_load_existing_database(ship_name)
            if existing_db:
                self.vector_databases[ship_name] = existing_db
                return existing_db

        # 构建新的向量数据库
        # 设置向量化模型
        set_embedding_model_type("qwen")

        # 获取历史数据
        filtered_data = fetch_ship_data(ship_name, "1990-01-01", "2025-08-03")

        if not filtered_data:
            return None

        # 分块处理
        blocks = segment_by_action(filtered_data)

        # 生成时间窗口和向量化
        all_windows = []
        valid_blocks = [block for block in blocks if block['size'] > 1]

        for i, block in enumerate(valid_blocks, 1):
            windows = create_time_windows(
                block, ship_name,
                window_hours=12, stride_hours=12,
                geohash_precision=7, enable_vectorization=True
            )
            all_windows.extend(windows)

        # 构建向量数据库
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        db_name = f"databases/online_faiss_db_{ship_name}_{timestamp}"

        if FAISS_AVAILABLE:
            vector_db = FAISSVectorDatabase(db_name)
            vector_db.build_from_windows_data(all_windows, ship_name, index_type="IVF")
        else:
            vector_db = VectorDatabase(db_name)
            vector_db.build_from_windows_data(all_windows, ship_name)

        # 缓存数据库
        self.vector_databases[ship_name] = vector_db

        return vector_db

    def _try_load_existing_database(self, ship_name: str):
        """尝试加载已存在的数据库文件"""
        import glob

        # 查找匹配的数据库文件
        pattern = f"databases/*{ship_name}*"
        db_files = glob.glob(pattern + ".db")

        if not db_files:
            return None

        # 使用最新的数据库文件
        latest_db_file = max(db_files, key=os.path.getctime)
        db_path = latest_db_file[:-3]  # 移除 .db 后缀

        try:
            # 检查是否是 FAISS 数据库
            if "faiss" in db_path.lower() and FAISS_AVAILABLE:
                vector_db = FAISSVectorDatabase(db_path)
                if vector_db._load_faiss_index():
                    return vector_db
            else:
                vector_db = VectorDatabase(db_path)
                return vector_db

        except Exception as e:
            return None

        return None

    def process_new_trajectory(self, ship_name: str, trajectory_coords: List[tuple]):
        """
        处理新的航迹数据
        Args:
            ship_name: 舰船名称
            trajectory_coords: 12小时航迹坐标列表 [(lat, lon), ...]
        Returns:
            处理后的向量和元数据
        """
        # 第一步：坐标文本化 (与离线处理完全相同)
        # 转换坐标格式
        coordinates = [{'lat': lat, 'lng': lon} for lat, lon in trajectory_coords]

        # 使用 coordinates_to_geohash 函数
        geohash_list = coordinates_to_geohash(coordinates, precision=7)
        geohash_sentence = ' '.join(geohash_list)

        # 第二步：创建身份文本 (与离线处理完全相同)
        identity_text = create_identity_text(ship_name, geohash_sentence)

        # 第三步：文本向量化 (使用相同的模型)
        query_vector = text_to_vector(identity_text)

        return {
            'ship_name': ship_name,
            'trajectory_coords': trajectory_coords,
            'geohash_sentence': geohash_sentence,
            'identity_text': identity_text,
            'query_vector': query_vector,
            'coordinate_count': len(trajectory_coords)
        }

    def similarity_search_and_classify(self, query_data: Dict, vector_db, top_k: int = 5):
        """
        相似度搜索和分类决策
        Args:
            query_data: 处理后的查询数据
            vector_db: 向量数据库
            top_k: 返回最相似的前k个结果
        Returns:
            分类结果和详细信息
        """
        query_vector = query_data['query_vector']

        try:
            # 使用向量数据库进行相似度搜索
            if isinstance(vector_db, FAISSVectorDatabase):
                # FAISS 搜索
                similar_results = vector_db.search_similar_vectors(
                    query_vector,
                    top_k=top_k
                )

                # 转换格式以统一处理
                similar_vectors = []
                for faiss_id, score, metadata in similar_results:
                    similar_vectors.append({
                        'id': faiss_id,
                        'similarity': score,
                        'action': metadata['action'],
                        'metadata': metadata
                    })
            else:
                # 基础数据库搜索
                similar_results = vector_db.find_similar_vectors(
                    query_vector,
                    top_k=top_k,
                    similarity_threshold=0.3
                )

                similar_vectors = []
                for vector_id, similarity, metadata in similar_results:
                    similar_vectors.append({
                        'id': vector_id,
                        'similarity': similarity,
                        'action': metadata['action'],
                        'metadata': metadata
                    })

            if not similar_vectors:
                return {
                    'predicted_action': '未知',
                    'confidence': 0.0,
                    'message': '未找到相似的历史样本',
                    'similar_samples': []
                }



            # 投票决策：统计各行为类型的得分
            action_votes = {}
            action_similarities = {}

            for sample in similar_vectors:
                action = sample['action']
                similarity = sample['similarity']

                if action not in action_votes:
                    action_votes[action] = 0
                    action_similarities[action] = []

                action_votes[action] += 1
                action_similarities[action].append(similarity)

            # 计算每种行为的综合得分（投票数 + 平均相似度）
            action_scores = {}
            for action in action_votes:
                vote_score = action_votes[action] / len(similar_vectors)  # 投票比例
                avg_similarity = np.mean(action_similarities[action])     # 平均相似度
                max_similarity = np.max(action_similarities[action])      # 最高相似度

                # 综合得分：投票权重0.4 + 平均相似度权重0.6
                combined_score = vote_score * 0.4 + avg_similarity * 0.6

                action_scores[action] = {
                    'vote_count': action_votes[action],
                    'vote_ratio': vote_score,
                    'avg_similarity': avg_similarity,
                    'max_similarity': max_similarity,
                    'combined_score': combined_score
                }

            # 确定最终预测结果
            best_action = max(action_scores.keys(), key=lambda x: action_scores[x]['combined_score'])
            confidence = min(action_scores[best_action]['combined_score'], 1.0)  # 限制在0-1之间



            return {
                'predicted_action': best_action,
                'confidence': confidence,
                'action_scores': action_scores,
                'similar_samples': similar_vectors,
                'query_info': query_data
            }

        except Exception as e:
            print(f"❌ 相似度搜索失败: {e}")
            return {
                'predicted_action': '错误',
                'confidence': 0.0,
                'message': f'搜索失败: {e}',
                'similar_samples': []
            }

    def generate_explanation(self, result: Dict):
        """
        生成可解释的分类结果
        Args:
            result: 分类结果
        Returns:
            格式化的解释文本
        """
        if result['predicted_action'] in ['未知', '错误']:
            return f"❌ {result.get('message', '分类失败')}"

        explanation = []
        explanation.append(f"🎯 **预测行为**: {result['predicted_action']}")
        explanation.append(f"🔥 **置信度**: {result['confidence']:.1%}")

        # 判断依据
        explanation.append(f"\n📊 **判断依据**:")
        action_scores = result['action_scores']

        for action, scores in action_scores.items():
            explanation.append(f"  • {action}:")
            explanation.append(f"    - 投票数: {scores['vote_count']}/{len(result['similar_samples'])} ({scores['vote_ratio']:.1%})")
            explanation.append(f"    - 平均相似度: {scores['avg_similarity']:.1%}")
            explanation.append(f"    - 最高相似度: {scores['max_similarity']:.1%}")
            explanation.append(f"    - 综合得分: {scores['combined_score']:.1%}")

        # 最相似的历史样本
        explanation.append(f"\n🔍 **最相似的历史样本**:")
        similar_samples = result['similar_samples'][:3]  # 只显示前3个

        for i, sample in enumerate(similar_samples, 1):
            metadata = sample['metadata']
            explanation.append(f"  {i}. **{sample['action']}** (相似度: {sample['similarity']:.1%})")
            explanation.append(f"     - 向量维度: {metadata.get('vector_dim', 'Unknown')}")
            explanation.append(f"     - 坐标点数: {metadata.get('coordinate_count', 'Unknown')}")
            if 'time_range' in metadata:
                explanation.append(f"     - 时间范围: {metadata['time_range']}")

        # 查询信息
        query_info = result['query_info']
        explanation.append(f"\n📍 **查询航迹信息**:")
        explanation.append(f"  - 舰船名称: {query_info['ship_name']}")
        explanation.append(f"  - 坐标点数: {query_info['coordinate_count']}")
        explanation.append(f"  - 向量维度: {len(query_info['query_vector'])}")

        return '\n'.join(explanation)

    def online_inference(self, ship_name: str, trajectory_coords: List[tuple], top_k: int = 5):
        """
        完整的在线推理流程
        Args:
            ship_name: 舰船名称
            trajectory_coords: 12小时航迹坐标列表
            top_k: 返回最相似的前k个结果
        Returns:
            完整的分类结果和解释
        """
        try:
            # 第一步：加载或构建舰船的向量数据库
            vector_db = self.load_or_build_ship_database(ship_name)

            if vector_db is None:
                return {
                    'success': False,
                    'message': f'无法获取 {ship_name} 的历史数据',
                    'predicted_action': '未知',
                    'confidence': 0.0
                }

            # 第二步：处理新航迹数据
            query_data = self.process_new_trajectory(ship_name, trajectory_coords)

            # 第三步：相似度搜索和分类决策
            result = self.similarity_search_and_classify(query_data, vector_db, top_k)

            # 第四步：生成可解释的结论
            explanation = self.generate_explanation(result)

            # 组装最终结果
            final_result = {
                'success': True,
                'ship_name': ship_name,
                'predicted_action': result['predicted_action'],
                'confidence': result['confidence'],
                'explanation': explanation,
                'detailed_result': result,
                'query_data': query_data
            }

            return final_result

        except Exception as e:
            import traceback
            traceback.print_exc()

            return {
                'success': False,
                'message': f'推理过程出错: {e}',
                'predicted_action': '错误',
                'confidence': 0.0
            }

# 全局在线推理引擎实例
_online_engine = None

def get_online_inference_engine():
    """获取全局在线推理引擎实例"""
    global _online_engine
    if _online_engine is None:
        _online_engine = OnlineInferenceEngine()
    return _online_engine

def online_trajectory_classification(ship_name: str, trajectory_coords: List[tuple], top_k: int = 5):
    """
    在线航迹行为分类的主要接口
    Args:
        ship_name: 舰船名称
        trajectory_coords: 12小时航迹坐标列表 [(lat, lon), ...]
        top_k: 返回最相似的前k个结果
    Returns:
        分类结果字典
    """
    engine = get_online_inference_engine()
    return engine.online_inference(ship_name, trajectory_coords, top_k)

def demo_online_inference():
    """演示在线推理功能"""
    print(f"\n🎬 阶段二：在线查询演示")
    print(f"=" * 60)

    # 示例1：已知舰船（文森号航空母舰）
    print(f"\n📋 示例1：已知舰船的新航迹分类")

    ship_name = "文森号航空母舰"
    # 模拟12小时的航迹数据
    trajectory_coords = [
        (35.1234, 139.5678),
        (35.1244, 139.5688),
        (35.1254, 139.5698),
        (35.1264, 139.5708),
        (35.1274, 139.5718),
        (35.1284, 139.5728),
        (35.1294, 139.5738),
        (35.1304, 139.5748)
    ]

    result1 = online_trajectory_classification(ship_name, trajectory_coords, top_k=5)

    if result1['success']:
        print(f"\n📊 分类结果:")
        print(result1['explanation'])
    else:
        print(f"❌ 分类失败: {result1['message']}")

    print(f"\n🎉 在线推理演示完成！")

# 添加缺失的函数
def set_embedding_model_type(model_type: str):
    """设置向量化模型类型"""
    global _model_type
    _model_type = model_type

def create_identity_text(ship_name: str, geohash_sentence: str) -> str:
    """创建身份文本"""
    return f"{ship_name} {geohash_sentence}"

def get_qwen_embedding_model():
    """获取Qwen3-Embedding嵌入模型"""
    global _embedding_model

    if _embedding_model is None:
        try:
            # 方法1：尝试加载Qwen3-Embedding GGUF模型
            if LLAMA_CPP_AVAILABLE:
                model_path = "models/qwen3-embedding-0.6b.gguf"
                if os.path.exists(model_path):
                    print("使用 Qwen3-Embedding GGUF 模型")
                    _embedding_model = Llama(
                        model_path=model_path,
                        embedding=True,
                        verbose=False
                    )
                    return _embedding_model

            # 方法2：使用transformers加载Qwen3-Embedding标准模型
            if TRANSFORMERS_AVAILABLE:
                try:
                    from transformers import AutoTokenizer, AutoModel
                    model_name = "Qwen/Qwen3-Embedding-0.6B"  # 正确的嵌入模型
                    print(f"使用 Qwen3-Embedding 标准模型: {model_name}")
                    _embedding_model = {
                        'tokenizer': AutoTokenizer.from_pretrained(model_name),
                        'model': AutoModel.from_pretrained(model_name)
                    }
                    return _embedding_model
                except Exception as e:
                    print(f"加载 Qwen3-Embedding 失败: {e}")

            # 方法3：备选sentence-transformers
            if SENTENCE_TRANSFORMERS_AVAILABLE:
                print("使用 sentence-transformers 备选模型")
                _embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
                return _embedding_model

        except Exception as e:
            print(f"加载向量化模型失败: {e}")

    return _embedding_model

def text_to_vector(text: str) -> np.ndarray:
    """
    第五步：文本向量化 (Text Vectorization)
    将身份文本转换为向量表示
    """
    global _model_type, _embedding_model

    # 增加调用计数器
    if not hasattr(text_to_vector, 'call_count'):
        text_to_vector.call_count = 0
    text_to_vector.call_count += 1

    try:
        if _model_type == "qwen":
            model = get_qwen_embedding_model()

            if model is not None:
                if LLAMA_CPP_AVAILABLE and hasattr(model, 'embed'):
                    # 使用llama-cpp-python的Qwen模型
                    embedding = model.embed(text)
                    return np.array(embedding, dtype=np.float32)

                elif TRANSFORMERS_AVAILABLE and isinstance(model, dict):
                    # 使用transformers的Qwen模型
                    tokenizer = model['tokenizer']
                    model_obj = model['model']

                    inputs = tokenizer(text, return_tensors='pt', truncation=True, max_length=512)
                    with torch.no_grad():
                        outputs = model_obj(**inputs)
                        # 使用[CLS]标记的嵌入或平均池化
                        embeddings = outputs.last_hidden_state.mean(dim=1)
                        return embeddings.numpy().flatten().astype(np.float32)

                elif SENTENCE_TRANSFORMERS_AVAILABLE:
                    # 使用sentence-transformers
                    embedding = model.encode(text)
                    return np.array(embedding, dtype=np.float32)

        elif _model_type == "sentence_transformer" and SENTENCE_TRANSFORMERS_AVAILABLE:
            if _embedding_model is None:
                _embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            embedding = _embedding_model.encode(text)
            return np.array(embedding, dtype=np.float32)

    except Exception as e:
        print(f"向量化失败，使用哈希方法: {e}")

    # 备选方案：哈希向量化
    return _hash_vectorization(text)

def _hash_vectorization(text: str) -> np.ndarray:
    """哈希向量化备选方案"""
    import hashlib

    # 使用多种哈希算法创建更丰富的向量
    hash_methods = [
        hashlib.md5(text.encode()).hexdigest(),
        hashlib.sha1(text.encode()).hexdigest(),
        hashlib.sha256(text.encode()).hexdigest()[:32],  # 截取前32位
    ]

    # 将哈希值转换为数值向量
    vector_parts = []
    for hash_hex in hash_methods:
        # 将十六进制字符转换为数值
        nums = [int(hash_hex[i:i+2], 16) for i in range(0, len(hash_hex), 2)]
        vector_parts.extend(nums)

    # 创建基础向量
    base_vector = np.array(vector_parts, dtype=np.float32)

    # 扩展向量维度到合理大小
    target_dim = 1024  # 目标维度
    if len(base_vector) < target_dim:
        # 重复和填充
        repeat_times = target_dim // len(base_vector) + 1
        extended_vector = np.tile(base_vector, repeat_times)[:target_dim]
    else:
        extended_vector = base_vector[:target_dim]

    # 标准化向量
    norm = np.linalg.norm(extended_vector)
    if norm > 0:
        extended_vector = extended_vector / norm

    return extended_vector

def set_embedding_model_type(model_type: str):
    """设置向量化模型类型"""
    global _model_type, _embedding_model
    _model_type = model_type
    _embedding_model = None  # 重置模型缓存

def create_time_windows(block, ship_name, window_hours=12, stride_hours=12,
                       geohash_precision=7, enable_vectorization=True):
    """创建时间窗口"""
    windows = []

    # 简化实现，创建一个窗口
    coordinates = [{'lat': point['lat'], 'lng': point['lng']}
                  for point in block['data_points']]

    geohash_sentence = coordinates_to_geohash(coordinates, precision=geohash_precision)
    identity_text = create_identity_text(ship_name, geohash_sentence)

    vector = None
    if enable_vectorization:
        vector = text_to_vector(identity_text)

    window = {
        'action': block['action'],
        'identity_text': identity_text,
        'geohash_sentence': geohash_sentence,
        'coordinate_count': len(coordinates),
        'vector': vector,
        'vector_dim': len(vector) if vector is not None else 0
    }

    windows.append(window)
    return windows

def interactive_input():
    """交互式输入"""
    print("🚀 航迹行为识别系统 - 交互模式")
    print("=" * 50)

    while True:
        print("\n请选择操作模式:")
        print("1. 构建历史数据库（离线准备）")
        print("2. 在线航迹分类")
        print("3. 退出")

        choice = input("请输入选择 (1-3): ").strip()

        if choice == "1":
            ship_name = input("请输入舰船名称 (默认: 文森号航空母舰): ").strip()
            if not ship_name:
                ship_name = "文森号航空母舰"

            print(f"正在为 {ship_name} 构建历史数据库...")
            build_historical_database(ship_name)

        elif choice == "2":
            ship_name = input("请输入舰船名称: ").strip()
            if not ship_name:
                print("❌ 舰船名称不能为空")
                continue

            print("请输入12小时航迹坐标 (格式: 纬度,经度，每行一个坐标):")
            print("输入完成后，输入空行结束:")

            trajectory_coords = []
            while True:
                coord_input = input().strip()
                if not coord_input:
                    break

                try:
                    lat, lon = map(float, coord_input.split(','))
                    trajectory_coords.append((lat, lon))
                except ValueError:
                    print("❌ 格式错误，请使用 '纬度,经度' 格式")

            if len(trajectory_coords) == 0:
                print("❌ 未输入任何坐标")
                continue

            print(f"开始分类 {len(trajectory_coords)} 个坐标点...")
            result = online_trajectory_classification(ship_name, trajectory_coords)

            if result['success']:
                print("\n📊 分类结果:")
                print(f"预测行为: {result['predicted_action']}")
                print(f"置信度: {result['confidence']:.1%}")
                print("\n详细解释:")
                print(result['explanation'])
            else:
                print(f"❌ 分类失败: {result['message']}")

        elif choice == "3":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")

def command_line_input():
    """命令行参数输入"""
    import sys

    if len(sys.argv) < 2:
        print("用法:")
        print("  python 'src/behavior recognition.py' build <舰船名称>")
        print("  python 'src/behavior recognition.py' classify <舰船名称> <坐标文件>")
        return

    command = sys.argv[1]

    if command == "build":
        if len(sys.argv) < 3:
            print("❌ 请提供舰船名称")
            return

        ship_name = sys.argv[2]
        print(f"为 {ship_name} 构建历史数据库...")
        build_historical_database(ship_name)

    elif command == "classify":
        if len(sys.argv) < 4:
            print("❌ 请提供舰船名称和坐标文件")
            return

        ship_name = sys.argv[2]
        coord_file = sys.argv[3]

        try:
            trajectory_coords = []
            with open(coord_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        lat, lon = map(float, line.split(','))
                        trajectory_coords.append((lat, lon))

            result = online_trajectory_classification(ship_name, trajectory_coords)

            if result['success']:
                print(f"预测行为: {result['predicted_action']}")
                print(f"置信度: {result['confidence']:.1%}")
            else:
                print(f"分类失败: {result['message']}")

        except Exception as e:
            print(f"❌ 处理文件失败: {e}")
    else:
        print("❌ 未知命令")

def build_historical_database(ship_name: str):
    """构建历史数据库"""
    filtered_data = fetch_ship_data(ship_name, "1990-01-01", "2025-08-03")

    if not filtered_data:
        print("❌ 无法获取数据")
        return

    blocks = segment_by_action(filtered_data)

    print("=== 生成时间窗口和向量化 ===")
    all_windows = []
    total_vectors_by_action = {}

    valid_blocks = [block for block in blocks if block['size'] > 1]

    for i, block in enumerate(valid_blocks, 1):
        windows = create_time_windows(block, ship_name, window_hours=12, stride_hours=12,
                                    geohash_precision=7, enable_vectorization=True)
        all_windows.extend(windows)

        action = block['action']
        if action not in total_vectors_by_action:
            total_vectors_by_action[action] = 0

        for window in windows:
            if window['vector'] is not None:
                total_vectors_by_action[action] += 1

    total_vectors = sum(total_vectors_by_action.values())

    print(f"✅ 阶段一完成: {len(all_windows)} 个时间窗口，{total_vectors} 个向量")

    vector_db = step6_build_vector_database(all_windows, ship_name)
    print(f"✅ {ship_name} 历史数据库构建完成")

def main():
    """主函数"""
    import sys

    # 检查是否有命令行参数
    if len(sys.argv) > 1:
        command_line_input()
    else:
        # 默认交互模式
        interactive_input()


if __name__ == "__main__":
    main()
