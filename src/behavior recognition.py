

import requests
import json
from typing import Dict,List,Optional
from datetime import datetime,timedelta
import pygeohash as pgh
import numpy as np
import hashlib
import os
import pickle
import sqlite3
try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False
    print("FAISS 不可用，将使用基础向量数据库")
try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    print("sentence-transformers 不可用，将使用简单哈希向量化方法")

try:
    from transformers import AutoTokenizer, AutoModel
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    print("transformers 不可用")

try:
    from llama_cpp import Llama
    LLAMA_CPP_AVAILABLE = True
except ImportError:
    LLAMA_CPP_AVAILABLE = False
    print("llama-cpp-python 不可用")

#API配置
API_BASE_URL = "http://**************:23071"
API_ENDPOINT = "/api/v1/ship/active/trend"

def decode_unicode_in_dict(obj):
    """
    dig
    """
def fetch_ship_data(ship_id:str,start_time:str,end_time:str) -> Optional[Dict]:
    """
    从API获取船舶历史数据
    Args:
        ship_id (str): 船舶ID
        start_time (str): 开始时间
        end_time (str): 结束时间
    Returns:
        Dict: 历史数据字典或None
    """
    try:
        #构建完整API URL
        url = f"{API_BASE_URL}{API_ENDPOINT}"
        #准备请求参数
        params ={
            "ship_id":ship_id,
            "start_time":start_time,
            "end_time":end_time
        }
        proxies = {
            'http': None,
            'https': None
        }
        print(f"正在请求API: {url} with params {params}")

        #发送GET请求
        response = requests.get(url,params=params,timeout=30,proxies=proxies)

        print(f"HTTP状态码：{response.status_code}")
        if response.status_code == 200:
            #解析JSON数据
            data = response.json()
            print("请求成功")
            # 第一步：数据解析和筛选
            trace_data = data.get("trace_data", [])
            candidate_sets = ["编队", "航渡", "停靠", "巡航"]

            filtered_data = []
            for item in trace_data:
                if item["action"] in candidate_sets:
                    filtered_data.append(item)

            print(f"筛选出 {len(filtered_data)} 个有效数据点")
            return filtered_data
            return data

        else:
            print(f"请求失败，状态码：{response.status_code}")
            try:
                error_info = response.json()
                print(f"错误详情：{json.dump(error_info,ernsure_ascii=False,indent=2)}")
            except:
                print(f"错误信息{response.text}")
            return None

    except requests.exceptions.Timeout:
        print("请求超时")
        return None
    except requests.exceptions.ConnectionError:
        print("无法连接到API服务器")
        return None
    except json.JSONDecodeError:
        print("无法解析JSON数据")
        return None
    except Exception as e:
        print(f"发生错误：{e}")
        return None
def segment_by_action(filtered_data):
    """
    第二步：按action进行事件分块
    将连续相同的action合并为事件区块
    """
    if not filtered_data:
        return []
    #找出所有区块的起始位置
    block_starts=[]
    for i in range(len(filtered_data)):
        current_action=filtered_data[i]["action"]
        if i==0 or filtered_data[i-1]["action"]!=current_action:
            block_starts.append(i)
    blocks=[]
    for i in range(len(block_starts)):
        start_pos=block_starts[i]
        #计算结束位置
        if i==len(block_starts)-1:
            end_pos = len(filtered_data)-1
        else:
            end_pos=block_starts[i+1]-1
        #创建区块信息
        block={
            'block_id':i+1,
            'action':filtered_data[start_pos]['action'],
            'start_pos':start_pos,
            'end_pos':end_pos,
            'size':end_pos-start_pos+1,
            'data_points':filtered_data[start_pos:end_pos+1]  #包含该区块的所有数据点
        }
        blocks.append(block)
    return blocks
def parse_time(time_str):
    """
    将时间字符串转换为datetime对象

    """
    return datetime.strptime(time_str,"%Y-%m-%d %H:%M:%S")


def extract_window_coordinates(block, window_start, window_end):
    """
    从区块中提取指定时间窗口内的坐标数据点
    Args:
        block: 事件区块
        window_start: 窗口开始时间 (datetime对象)
        window_end: 窗口结束时间 (datetime对象)
    Returns:
        List: 窗口内的坐标数据点列表
    """
    window_points = []

    for point in block['data_points']:
        point_time = parse_time(point['scout_time'])

        # 检查数据点是否在时间窗口内
        if window_start <= point_time <= window_end:
            window_points.append({
                'lat': point['lat'],
                'lng': point['lng'],
                'scout_time': point['scout_time']
            })

    return window_points


def coordinates_to_geohash(coordinates, precision=7):
    """
    第四步：坐标文本化 (Coordinate Textualization)
    将坐标序列转换为Geohash字符串序列
    Args:
        coordinates: 坐标点列表，每个点包含lat和lng
        precision: Geohash精度 (6-8位，默认7位)
    Returns:
        str: Geohash字符串序列，用空格分隔
    """
    if not coordinates:
        return ""

    geohash_list = []
    for coord in coordinates:
        # 使用pygeohash库将经纬度转换为geohash
        geohash = pgh.encode(coord['lat'], coord['lng'], precision=precision)
        geohash_list.append(geohash)

    # 将geohash序列用空格连接成字符串
    geohash_sentence = " ".join(geohash_list)
    return geohash_sentence


# 全局变量：缓存向量化模型
_embedding_model = None
_model_type = "qwen"  # 可选: "qwen", "sentence_transformer", "hash"

class VectorDatabase:
    """向量数据库类，用于存储和检索航迹行为向量"""

    def __init__(self, db_path: str = "vector_database"):
        self.db_path = db_path
        self.db_file = f"{db_path}.db"
        self.vectors_file = f"{db_path}_vectors.pkl"
        self.metadata_file = f"{db_path}_metadata.json"

        # 创建数据库目录
        os.makedirs(os.path.dirname(self.db_file) if os.path.dirname(self.db_file) else ".", exist_ok=True)

        # 初始化数据库
        self._init_database()

    def _init_database(self):
        """初始化SQLite数据库"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        # 创建向量元数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS vector_metadata (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ship_name TEXT NOT NULL,
                action TEXT NOT NULL,
                identity_text TEXT,
                geohash_sentence TEXT,
                coordinate_count INTEGER,
                vector_dim INTEGER,
                vector_norm REAL,
                time_range TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                vector_id INTEGER UNIQUE
            )
        ''')

        # 创建行为统计表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS behavior_stats (
                action TEXT PRIMARY KEY,
                total_samples INTEGER,
                avg_vector_dim REAL,
                avg_coordinate_count REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_action ON vector_metadata(action)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_ship_name ON vector_metadata(ship_name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_vector_dim ON vector_metadata(vector_dim)')

        conn.commit()
        conn.close()

    def build_from_windows_data(self, windows_data: List[Dict], ship_name: str):
        """
        从时间窗口数据构建向量数据库
        Args:
            windows_data: 阶段一生成的时间窗口数据
            ship_name: 舰船名称
        """
        print(f"🔄 第六步：构建 {ship_name} 的向量数据库...")

        vectors = []
        metadata_list = []
        behavior_stats = {}

        valid_count = 0

        for i, window in enumerate(windows_data):
            if window['vector'] is not None:
                vector = window['vector']

                # 确保向量是一维的
                if vector.ndim > 1:
                    vector = vector.flatten()

                # 计算向量范数
                vector_norm = float(np.linalg.norm(vector))

                # 存储向量
                vectors.append(vector)

                # 存储元数据
                metadata = {
                    'vector_id': valid_count,
                    'ship_name': ship_name,
                    'action': window['action'],
                    'identity_text': window['identity_text'],
                    'geohash_sentence': window['geohash_sentence'],
                    'coordinate_count': window['coordinate_count'],
                    'vector_dim': len(vector),
                    'vector_norm': vector_norm,
                    'time_range': window.get('time_range', 'Unknown')
                }
                metadata_list.append(metadata)

                # 统计行为信息
                action = window['action']
                if action not in behavior_stats:
                    behavior_stats[action] = {
                        'count': 0,
                        'total_dim': 0,
                        'total_coords': 0
                    }

                behavior_stats[action]['count'] += 1
                behavior_stats[action]['total_dim'] += len(vector)
                behavior_stats[action]['total_coords'] += window['coordinate_count']

                valid_count += 1

        # 保存向量数据
        self._save_vectors(vectors)

        # 保存元数据到数据库
        self._save_metadata_to_db(metadata_list)

        # 保存行为统计
        self._save_behavior_stats(behavior_stats)

        # 保存元数据到JSON文件（备份）
        self._save_metadata_to_json(metadata_list, behavior_stats)

        print(f"✅ 向量数据库构建完成:")
        print(f"  - 总向量数: {len(vectors)}")
        print(f"  - 数据库文件: {self.db_file}")
        print(f"  - 向量文件: {self.vectors_file}")
        print(f"  - 元数据文件: {self.metadata_file}")

        return len(vectors)

    def _save_vectors(self, vectors: List[np.ndarray]):
        """保存向量数据到pickle文件"""
        with open(self.vectors_file, 'wb') as f:
            pickle.dump(vectors, f)

    def _save_metadata_to_db(self, metadata_list: List[Dict]):
        """保存元数据到SQLite数据库"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        # 清空现有数据
        cursor.execute('DELETE FROM vector_metadata')

        # 插入新数据
        for metadata in metadata_list:
            cursor.execute('''
                INSERT INTO vector_metadata
                (ship_name, action, identity_text, geohash_sentence, coordinate_count,
                 vector_dim, vector_norm, time_range, vector_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                metadata['ship_name'],
                metadata['action'],
                metadata['identity_text'],
                metadata['geohash_sentence'],
                metadata['coordinate_count'],
                metadata['vector_dim'],
                metadata['vector_norm'],
                metadata['time_range'],
                metadata['vector_id']
            ))

        conn.commit()
        conn.close()

    def _save_behavior_stats(self, behavior_stats: Dict):
        """保存行为统计信息"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        # 清空现有统计
        cursor.execute('DELETE FROM behavior_stats')

        # 插入新统计
        for action, stats in behavior_stats.items():
            avg_dim = stats['total_dim'] / stats['count']
            avg_coords = stats['total_coords'] / stats['count']

            cursor.execute('''
                INSERT INTO behavior_stats (action, total_samples, avg_vector_dim, avg_coordinate_count)
                VALUES (?, ?, ?, ?)
            ''', (action, stats['count'], avg_dim, avg_coords))

        conn.commit()
        conn.close()

    def _save_metadata_to_json(self, metadata_list: List[Dict], behavior_stats: Dict):
        """保存元数据到JSON文件（备份）"""
        data = {
            'created_at': datetime.now().isoformat(),
            'total_vectors': len(metadata_list),
            'behavior_stats': {
                action: {
                    'total_samples': stats['count'],
                    'avg_vector_dim': stats['total_dim'] / stats['count'],
                    'avg_coordinate_count': stats['total_coords'] / stats['count']
                }
                for action, stats in behavior_stats.items()
            },
            'metadata': metadata_list
        }

        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

    def load_vectors(self) -> List[np.ndarray]:
        """加载向量数据"""
        if not os.path.exists(self.vectors_file):
            raise FileNotFoundError(f"向量文件不存在: {self.vectors_file}")

        with open(self.vectors_file, 'rb') as f:
            return pickle.load(f)

    def query_metadata(self, action: Optional[str] = None,
                      ship_name: Optional[str] = None,
                      min_vector_dim: Optional[int] = None,
                      max_vector_dim: Optional[int] = None) -> List[Dict]:
        """查询元数据"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        query = "SELECT * FROM vector_metadata WHERE 1=1"
        params = []

        if action:
            query += " AND action = ?"
            params.append(action)

        if ship_name:
            query += " AND ship_name = ?"
            params.append(ship_name)

        if min_vector_dim:
            query += " AND vector_dim >= ?"
            params.append(min_vector_dim)

        if max_vector_dim:
            query += " AND vector_dim <= ?"
            params.append(max_vector_dim)

        cursor.execute(query, params)
        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        conn.close()
        return results

    def get_behavior_stats(self) -> Dict:
        """获取行为统计信息"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM behavior_stats")
        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        conn.close()
        return {row['action']: row for row in results}

    def cosine_similarity_db(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """计算余弦相似度"""
        # 处理不同维度的向量
        min_dim = min(len(vec1), len(vec2))
        v1 = vec1[:min_dim]
        v2 = vec2[:min_dim]

        # 计算余弦相似度
        dot_product = np.dot(v1, v2)
        norm1 = np.linalg.norm(v1)
        norm2 = np.linalg.norm(v2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)

    def find_similar_vectors(self, query_vector: np.ndarray,
                           top_k: int = 5,
                           action_filter: Optional[str] = None,
                           similarity_threshold: float = 0.0):
        """
        查找相似向量
        Args:
            query_vector: 查询向量
            top_k: 返回最相似的前k个结果
            action_filter: 行为类型过滤
            similarity_threshold: 相似度阈值
        Returns:
            List of (vector_id, similarity, metadata)
        """
        # 加载向量和元数据
        vectors = self.load_vectors()
        metadata_list = self.query_metadata(action=action_filter)

        # 计算相似度
        similarities = []

        for metadata in metadata_list:
            vector_id = metadata['vector_id']
            if vector_id < len(vectors):
                similarity = self.cosine_similarity_db(query_vector, vectors[vector_id])

                if similarity >= similarity_threshold:
                    similarities.append((vector_id, similarity, metadata))

        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)

        return similarities[:top_k]

    def get_database_info(self) -> Dict:
        """获取数据库信息"""
        info = {
            'database_files': {
                'db_file': self.db_file,
                'vectors_file': self.vectors_file,
                'metadata_file': self.metadata_file
            },
            'file_exists': {
                'db_file': os.path.exists(self.db_file),
                'vectors_file': os.path.exists(self.vectors_file),
                'metadata_file': os.path.exists(self.metadata_file)
            }
        }

        if info['file_exists']['db_file']:
            # 获取统计信息
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            cursor.execute("SELECT COUNT(*) FROM vector_metadata")
            info['total_vectors'] = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(DISTINCT action) FROM vector_metadata")
            info['unique_actions'] = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(DISTINCT ship_name) FROM vector_metadata")
            info['unique_ships'] = cursor.fetchone()[0]

            conn.close()

        if info['file_exists']['vectors_file']:
            vectors = self.load_vectors()
            info['vector_file_size'] = len(vectors)
            if vectors:
                info['sample_vector_dim'] = len(vectors[0])

        return info

class FAISSVectorDatabase:
    """基于 FAISS 的高性能向量数据库"""

    def __init__(self, db_path: str = "faiss_vector_database"):
        self.db_path = db_path
        self.db_file = f"{db_path}.db"
        self.faiss_index_file = f"{db_path}_faiss.index"
        self.metadata_file = f"{db_path}_metadata.json"

        # FAISS 相关
        self.faiss_index = None
        self.vector_dimension = None
        self.index_type = "IVF"  # 可选: "Flat", "IVF", "HNSW"

        # 创建数据库目录
        os.makedirs(os.path.dirname(self.db_file) if os.path.dirname(self.db_file) else ".", exist_ok=True)

        # 初始化数据库
        self._init_database()

    def _init_database(self):
        """初始化SQLite数据库（存储元数据）"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        # 创建向量元数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS vector_metadata (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                faiss_id INTEGER UNIQUE,
                ship_name TEXT NOT NULL,
                action TEXT NOT NULL,
                identity_text TEXT,
                geohash_sentence TEXT,
                coordinate_count INTEGER,
                vector_dim INTEGER,
                vector_norm REAL,
                time_range TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建行为统计表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS behavior_stats (
                action TEXT PRIMARY KEY,
                total_samples INTEGER,
                avg_vector_dim REAL,
                avg_coordinate_count REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_faiss_id ON vector_metadata(faiss_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_action ON vector_metadata(action)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_ship_name ON vector_metadata(ship_name)')

        conn.commit()
        conn.close()

    def _create_faiss_index(self, vectors: List[np.ndarray], index_type: str = "IVF"):
        """创建 FAISS 索引"""
        if not FAISS_AVAILABLE:
            raise ImportError("FAISS 不可用，请安装: pip install faiss-cpu")

        # 标准化向量维度
        max_dim = max(len(v) for v in vectors)
        normalized_vectors = []

        for vector in vectors:
            if len(vector) < max_dim:
                # 零填充
                padded = np.zeros(max_dim, dtype=np.float32)
                padded[:len(vector)] = vector.astype(np.float32)
                normalized_vectors.append(padded)
            else:
                # 截断
                normalized_vectors.append(vector[:max_dim].astype(np.float32))

        vectors_matrix = np.array(normalized_vectors)
        self.vector_dimension = max_dim

        print(f"🔧 创建 FAISS 索引 (类型: {index_type}, 维度: {max_dim}, 向量数: {len(vectors)})")

        if index_type == "Flat":
            # 精确搜索，适合小数据集
            index = faiss.IndexFlatIP(max_dim)  # 内积相似度
        elif index_type == "IVF":
            # 倒排文件索引，适合中等数据集
            nlist = min(100, len(vectors) // 10)  # 聚类中心数
            quantizer = faiss.IndexFlatIP(max_dim)
            index = faiss.IndexIVFFlat(quantizer, max_dim, nlist)

            # 训练索引
            print(f"🏋️ 训练 IVF 索引 (聚类中心: {nlist})")
            index.train(vectors_matrix)
        elif index_type == "HNSW":
            # 分层导航小世界图，适合大数据集
            index = faiss.IndexHNSWFlat(max_dim, 32)
        else:
            raise ValueError(f"不支持的索引类型: {index_type}")

        # 添加向量到索引
        print(f"📥 添加向量到 FAISS 索引...")
        index.add(vectors_matrix)

        self.faiss_index = index
        return index

    def build_from_windows_data(self, windows_data: List[Dict], ship_name: str, index_type: str = "IVF"):
        """
        从时间窗口数据构建 FAISS 向量数据库
        Args:
            windows_data: 阶段一生成的时间窗口数据
            ship_name: 舰船名称
            index_type: FAISS索引类型 ("Flat", "IVF", "HNSW")
        """
        print(f"🔄 第六步：构建 {ship_name} 的 FAISS 向量数据库...")

        vectors = []
        metadata_list = []
        behavior_stats = {}

        valid_count = 0

        for i, window in enumerate(windows_data):
            if window['vector'] is not None:
                vector = window['vector']

                # 确保向量是一维的
                if vector.ndim > 1:
                    vector = vector.flatten()

                # 计算向量范数
                vector_norm = float(np.linalg.norm(vector))

                # 存储向量
                vectors.append(vector)

                # 存储元数据
                metadata = {
                    'faiss_id': valid_count,
                    'ship_name': ship_name,
                    'action': window['action'],
                    'identity_text': window['identity_text'],
                    'geohash_sentence': window['geohash_sentence'],
                    'coordinate_count': window['coordinate_count'],
                    'vector_dim': len(vector),
                    'vector_norm': vector_norm,
                    'time_range': window.get('time_range', 'Unknown')
                }
                metadata_list.append(metadata)

                # 统计行为信息
                action = window['action']
                if action not in behavior_stats:
                    behavior_stats[action] = {
                        'count': 0,
                        'total_dim': 0,
                        'total_coords': 0
                    }

                behavior_stats[action]['count'] += 1
                behavior_stats[action]['total_dim'] += len(vector)
                behavior_stats[action]['total_coords'] += window['coordinate_count']

                valid_count += 1

        # 创建 FAISS 索引
        self.index_type = index_type
        self._create_faiss_index(vectors, index_type)

        # 保存 FAISS 索引
        self._save_faiss_index()

        # 保存元数据到数据库
        self._save_metadata_to_db(metadata_list)

        # 保存行为统计
        self._save_behavior_stats(behavior_stats)

        # 保存元数据到JSON文件（备份）
        self._save_metadata_to_json(metadata_list, behavior_stats)

        print(f"✅ FAISS 向量数据库构建完成:")
        print(f"  - 总向量数: {len(vectors)}")
        print(f"  - 向量维度: {self.vector_dimension}")
        print(f"  - 索引类型: {index_type}")
        print(f"  - 数据库文件: {self.db_file}")
        print(f"  - FAISS索引文件: {self.faiss_index_file}")
        print(f"  - 元数据文件: {self.metadata_file}")

        return len(vectors)

    def _save_faiss_index(self):
        """保存 FAISS 索引到文件"""
        if self.faiss_index is not None:
            faiss.write_index(self.faiss_index, self.faiss_index_file)

    def _load_faiss_index(self):
        """从文件加载 FAISS 索引"""
        if os.path.exists(self.faiss_index_file):
            self.faiss_index = faiss.read_index(self.faiss_index_file)
            self.vector_dimension = self.faiss_index.d
            return True
        return False

    def _save_metadata_to_db(self, metadata_list: List[Dict]):
        """保存元数据到SQLite数据库"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        # 清空现有数据
        cursor.execute('DELETE FROM vector_metadata')

        # 插入新数据
        for metadata in metadata_list:
            cursor.execute('''
                INSERT INTO vector_metadata
                (faiss_id, ship_name, action, identity_text, geohash_sentence, coordinate_count,
                 vector_dim, vector_norm, time_range)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                metadata['faiss_id'],
                metadata['ship_name'],
                metadata['action'],
                metadata['identity_text'],
                metadata['geohash_sentence'],
                metadata['coordinate_count'],
                metadata['vector_dim'],
                metadata['vector_norm'],
                metadata['time_range']
            ))

        conn.commit()
        conn.close()

    def _save_behavior_stats(self, behavior_stats: Dict):
        """保存行为统计信息"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        # 清空现有统计
        cursor.execute('DELETE FROM behavior_stats')

        # 插入新统计
        for action, stats in behavior_stats.items():
            avg_dim = stats['total_dim'] / stats['count']
            avg_coords = stats['total_coords'] / stats['count']

            cursor.execute('''
                INSERT INTO behavior_stats (action, total_samples, avg_vector_dim, avg_coordinate_count)
                VALUES (?, ?, ?, ?)
            ''', (action, stats['count'], avg_dim, avg_coords))

        conn.commit()
        conn.close()

    def _save_metadata_to_json(self, metadata_list: List[Dict], behavior_stats: Dict):
        """保存元数据到JSON文件（备份）"""
        data = {
            'created_at': datetime.now().isoformat(),
            'total_vectors': len(metadata_list),
            'vector_dimension': self.vector_dimension,
            'index_type': self.index_type,
            'faiss_available': FAISS_AVAILABLE,
            'behavior_stats': {
                action: {
                    'total_samples': stats['count'],
                    'avg_vector_dim': stats['total_dim'] / stats['count'],
                    'avg_coordinate_count': stats['total_coords'] / stats['count']
                }
                for action, stats in behavior_stats.items()
            },
            'metadata': metadata_list
        }

        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

    def search_similar_vectors(self, query_vector: np.ndarray,
                             top_k: int = 5,
                             action_filter: Optional[str] = None):
        """
        使用 FAISS 进行高效相似度搜索
        Args:
            query_vector: 查询向量
            top_k: 返回最相似的前k个结果
            action_filter: 行为类型过滤
        Returns:
            List of (faiss_id, similarity_score, metadata)
        """
        if self.faiss_index is None:
            if not self._load_faiss_index():
                raise ValueError("FAISS 索引未加载")

        # 标准化查询向量
        if len(query_vector) < self.vector_dimension:
            padded_query = np.zeros(self.vector_dimension, dtype=np.float32)
            padded_query[:len(query_vector)] = query_vector.astype(np.float32)
        else:
            padded_query = query_vector[:self.vector_dimension].astype(np.float32)

        # 重塑为二维数组
        query_matrix = padded_query.reshape(1, -1)

        # FAISS 搜索
        if self.index_type == "IVF":
            # 设置搜索参数
            self.faiss_index.nprobe = min(10, self.faiss_index.nlist)

        scores, indices = self.faiss_index.search(query_matrix, top_k * 2)  # 多搜索一些以便过滤

        # 获取元数据
        results = []
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        for i, (score, faiss_id) in enumerate(zip(scores[0], indices[0])):
            if faiss_id == -1:  # FAISS 返回 -1 表示无效结果
                continue

            # 查询元数据
            cursor.execute("SELECT * FROM vector_metadata WHERE faiss_id = ?", (int(faiss_id),))
            row = cursor.fetchone()

            if row:
                columns = [description[0] for description in cursor.description]
                metadata = dict(zip(columns, row))

                # 应用行为过滤
                if action_filter is None or metadata['action'] == action_filter:
                    # 将FAISS的L2距离转换为相似度分数 (0-1范围)
                    # L2距离越小越相似，所以使用 1/(1+distance) 转换
                    similarity_score = 1.0 / (1.0 + float(score))
                    results.append((int(faiss_id), similarity_score, metadata))

                    if len(results) >= top_k:
                        break

        conn.close()
        return results

    def get_database_info(self) -> Dict:
        """获取数据库信息"""
        info = {
            'database_files': {
                'db_file': self.db_file,
                'faiss_index_file': self.faiss_index_file,
                'metadata_file': self.metadata_file
            },
            'file_exists': {
                'db_file': os.path.exists(self.db_file),
                'faiss_index_file': os.path.exists(self.faiss_index_file),
                'metadata_file': os.path.exists(self.metadata_file)
            },
            'faiss_available': FAISS_AVAILABLE,
            'vector_dimension': self.vector_dimension,
            'index_type': self.index_type
        }

        if info['file_exists']['db_file']:
            # 获取统计信息
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            cursor.execute("SELECT COUNT(*) FROM vector_metadata")
            info['total_vectors'] = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(DISTINCT action) FROM vector_metadata")
            info['unique_actions'] = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(DISTINCT ship_name) FROM vector_metadata")
            info['unique_ships'] = cursor.fetchone()[0]

            conn.close()

        return info

def step6_build_vector_database(all_windows, ship_name, db_name=None, use_faiss=True):
    """
    第六步：构建并存储向量数据库
    Args:
        all_windows: 前五步生成的时间窗口数据
        ship_name: 舰船名称
        db_name: 数据库名称（可选）
        use_faiss: 是否使用 FAISS 向量数据库
    Returns:
        VectorDatabase 或 FAISSVectorDatabase 实例
    """
    print(f"\n=== 第六步：构建并存储向量数据库 ===")

    # 设置数据库名称
    if not db_name:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if use_faiss and FAISS_AVAILABLE:
            db_name = f"databases/faiss_vector_db_{ship_name}_{timestamp}"
        else:
            db_name = f"databases/vector_db_{ship_name}_{timestamp}"

    # 创建向量数据库实例
    if use_faiss and FAISS_AVAILABLE:
        print("🚀 使用 FAISS 高性能向量数据库")
        vector_db = FAISSVectorDatabase(db_name)
        # 构建 FAISS 向量数据库
        vector_count = vector_db.build_from_windows_data(all_windows, ship_name, index_type="IVF")
    else:
        if use_faiss:
            print("⚠️ FAISS 不可用，回退到基础向量数据库")
        else:
            print("📦 使用基础向量数据库")
        vector_db = VectorDatabase(db_name)
        # 构建基础向量数据库
        vector_count = vector_db.build_from_windows_data(all_windows, ship_name)

    # 显示数据库信息
    db_info = vector_db.get_database_info()

    if hasattr(vector_db, 'get_behavior_stats'):
        behavior_stats = vector_db.get_behavior_stats()
    else:
        behavior_stats = {}

    print(f"\n📊 向量数据库统计:")
    print(f"  - 数据库类型: {'FAISS' if isinstance(vector_db, FAISSVectorDatabase) else '基础'}")
    print(f"  - 总向量数: {db_info.get('total_vectors', 0)}")
    print(f"  - 向量维度: {db_info.get('vector_dimension', 'Unknown')}")
    print(f"  - 索引类型: {db_info.get('index_type', 'N/A')}")
    print(f"  - 行为类型数: {db_info.get('unique_actions', 0)}")
    print(f"  - 舰船数: {db_info.get('unique_ships', 0)}")

    if behavior_stats:
        print(f"\n📈 各行为类型统计:")
        for action, stats in behavior_stats.items():
            print(f"  {action}:")
            print(f"    样本数: {stats['total_samples']}")
            print(f"    平均向量维度: {stats['avg_vector_dim']:.1f}")
            print(f"    平均坐标点数: {stats['avg_coordinate_count']:.1f}")

    print(f"\n💾 数据库文件:")
    for file_type, file_path in db_info['database_files'].items():
        exists = "✅" if db_info['file_exists'][file_type] else "❌"
        print(f"  {exists} {file_type}: {file_path}")

    return vector_db

def demo_vector_database_query(vector_db):
    """演示向量数据库查询功能"""
    print(f"\n=== 演示向量数据库查询功能 ===")

    try:
        is_faiss = isinstance(vector_db, FAISSVectorDatabase)

        if hasattr(vector_db, 'query_metadata'):
            # 查询停靠行为的向量
            docking_metadata = vector_db.query_metadata(action="停靠")
            print(f"📍 停靠行为向量数: {len(docking_metadata)}")

            # 查询巡航行为的向量
            cruise_metadata = vector_db.query_metadata(action="巡航")
            print(f"🚢 巡航行为向量数: {len(cruise_metadata)}")

            # 查询航渡行为的向量
            transit_metadata = vector_db.query_metadata(action="航渡")
            print(f"🛤️ 航渡行为向量数: {len(transit_metadata)}")

            # 查询高维向量
            high_dim_metadata = vector_db.query_metadata(min_vector_dim=10000)
            print(f"📏 高维向量(>=10000维)数: {len(high_dim_metadata)}")
        else:
            print("⚠️ 当前数据库不支持元数据查询")
            docking_metadata = []

        # 演示相似度搜索
        if docking_metadata:
            print(f"\n🔍 演示{'FAISS' if is_faiss else '基础'}相似度搜索:")

            if is_faiss:
                # FAISS 搜索
                # 创建一个模拟查询向量（使用第一个停靠行为的特征）
                identity_text = docking_metadata[0]['identity_text']
                query_vector = text_to_vector(identity_text)

                # 使用 FAISS 搜索
                similar_vectors = vector_db.search_similar_vectors(
                    query_vector,
                    top_k=3,
                    action_filter="停靠"
                )

                print(f"  查询文本: {identity_text}")
                print(f"  FAISS 找到 {len(similar_vectors)} 个相似向量:")
                for i, (faiss_id, score, metadata) in enumerate(similar_vectors, 1):
                    print(f"    {i}. FAISS_ID:{faiss_id}, 得分:{score:.4f}, 行为:{metadata['action']}")
            else:
                # 基础搜索
                vectors = vector_db.load_vectors()
                query_vector_id = docking_metadata[0]['vector_id']
                query_vector = vectors[query_vector_id]

                similar_vectors = vector_db.find_similar_vectors(
                    query_vector,
                    top_k=3,
                    action_filter="停靠",
                    similarity_threshold=0.5
                )

                print(f"  查询向量: 停靠行为 (ID: {query_vector_id})")
                print(f"  基础搜索找到 {len(similar_vectors)} 个相似向量:")
                for i, (vector_id, similarity, metadata) in enumerate(similar_vectors, 1):
                    print(f"    {i}. ID:{vector_id}, 相似度:{similarity:.4f}, 维度:{metadata['vector_dim']}")

    except Exception as e:
        print(f"❌ 查询演示失败: {e}")
        import traceback
        traceback.print_exc()

def classify_new_trajectory_with_database(vector_db, new_trajectory_coords, ship_name, top_k=5):
    """
    使用向量数据库对新航迹进行分类
    Args:
        vector_db: 向量数据库实例
        new_trajectory_coords: 新航迹坐标列表 [(lat, lon), ...]
        ship_name: 舰船名称
        top_k: 返回最相似的前k个结果
    Returns:
        分类结果
    """
    print(f"\n=== 使用向量数据库进行航迹分类 ===")

    try:
        # 将新航迹转换为向量
        geohash_list = []
        for lat, lon in new_trajectory_coords:
            geohash = coordinates_to_geohash(lat, lon, precision=7)
            geohash_list.append(geohash)

        geohash_sentence = ' '.join(geohash_list)
        identity_text = create_identity_text(ship_name, geohash_sentence)
        new_vector = text_to_vector(identity_text)

        print(f"🆕 新航迹信息:")
        print(f"  坐标点数: {len(new_trajectory_coords)}")
        print(f"  身份文本: {identity_text}")
        print(f"  向量维度: {len(new_vector)}")

        # 查找最相似的向量
        similar_vectors = vector_db.find_similar_vectors(
            new_vector,
            top_k=top_k,
            similarity_threshold=0.3
        )

        if similar_vectors:
            # 统计各行为类型的相似度
            action_scores = {}
            for vector_id, similarity, metadata in similar_vectors:
                action = metadata['action']
                if action not in action_scores:
                    action_scores[action] = []
                action_scores[action].append(similarity)

            # 计算平均相似度
            action_avg_scores = {}
            for action, scores in action_scores.items():
                action_avg_scores[action] = {
                    'avg_similarity': np.mean(scores),
                    'max_similarity': np.max(scores),
                    'count': len(scores)
                }

            # 确定最可能的行为
            best_action = max(action_avg_scores.keys(),
                            key=lambda x: action_avg_scores[x]['avg_similarity'])
            confidence = action_avg_scores[best_action]['avg_similarity']

            print(f"\n🎯 分类结果:")
            print(f"  预测行为: {best_action}")
            print(f"  置信度: {confidence:.4f}")

            print(f"\n📊 各行为类型得分:")
            for action, scores in action_avg_scores.items():
                print(f"  {action}:")
                print(f"    平均相似度: {scores['avg_similarity']:.4f}")
                print(f"    最高相似度: {scores['max_similarity']:.4f}")
                print(f"    匹配样本数: {scores['count']}")

            print(f"\n🔍 最相似的历史样本:")
            for i, (vector_id, similarity, metadata) in enumerate(similar_vectors, 1):
                print(f"  {i}. {metadata['action']} (相似度: {similarity:.4f}, 维度: {metadata['vector_dim']})")

            return {
                'predicted_action': best_action,
                'confidence': confidence,
                'action_scores': action_avg_scores,
                'similar_vectors': similar_vectors
            }
        else:
            print(f"❌ 未找到相似的历史样本")
            return None

    except Exception as e:
        print(f"❌ 分类失败: {e}")
        return None

# ==================== 阶段二：在线查询 (Online Inference) ====================

class OnlineInferenceEngine:
    """在线推理引擎，用于实时航迹行为分类"""

    def __init__(self):
        self.vector_databases = {}  # 存储不同舰船的向量数据库
        self.current_ship_name = None

    def load_or_build_ship_database(self, ship_name: str, force_rebuild: bool = False):
        """
        加载或构建指定舰船的向量数据库
        Args:
            ship_name: 舰船名称
            force_rebuild: 是否强制重建数据库
        Returns:
            向量数据库实例
        """
        print(f"\n🔍 处理舰船: {ship_name}")

        # 检查是否已经有该舰船的数据库
        if ship_name in self.vector_databases and not force_rebuild:
            print(f"✅ 使用已缓存的 {ship_name} 向量数据库")
            return self.vector_databases[ship_name]

        # 尝试加载已存在的数据库文件
        if not force_rebuild:
            existing_db = self._try_load_existing_database(ship_name)
            if existing_db:
                self.vector_databases[ship_name] = existing_db
                return existing_db

        # 构建新的向量数据库
        print(f"🔄 为 {ship_name} 构建新的向量数据库...")

        # 设置向量化模型
        set_embedding_model_type("qwen")

        # 获取历史数据
        print(f"📡 从在线接口获取 {ship_name} 的历史数据...")
        filtered_data = fetch_ship_data(ship_name, "1990-01-01", "2025-08-03")

        if not filtered_data:
            print(f"❌ 未找到 {ship_name} 的历史数据")
            return None

        print(f"✅ 获取到 {len(filtered_data)} 个历史数据点")

        # 分块处理
        blocks = segment_by_action(filtered_data)

        # 生成时间窗口和向量化
        all_windows = []
        valid_blocks = [block for block in blocks if block['size'] > 1]

        print(f"🔄 处理 {len(valid_blocks)} 个行为区块...")

        for i, block in enumerate(valid_blocks, 1):
            if i % 5 == 1:  # 每5个区块显示一次进度
                print(f"  处理区块 {i}/{len(valid_blocks)}: {block['action']}")

            windows = create_time_windows(
                block, ship_name,
                window_hours=12, stride_hours=12,
                geohash_precision=7, enable_vectorization=True
            )
            all_windows.extend(windows)

        print(f"✅ 生成了 {len(all_windows)} 个时间窗口")

        # 构建向量数据库
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        db_name = f"databases/online_faiss_db_{ship_name}_{timestamp}"

        if FAISS_AVAILABLE:
            vector_db = FAISSVectorDatabase(db_name)
            vector_db.build_from_windows_data(all_windows, ship_name, index_type="IVF")
        else:
            vector_db = VectorDatabase(db_name)
            vector_db.build_from_windows_data(all_windows, ship_name)

        # 缓存数据库
        self.vector_databases[ship_name] = vector_db

        print(f"✅ {ship_name} 向量数据库构建完成")
        return vector_db

    def _try_load_existing_database(self, ship_name: str):
        """尝试加载已存在的数据库文件"""
        import glob

        # 查找匹配的数据库文件
        pattern = f"databases/*{ship_name}*"
        db_files = glob.glob(pattern + ".db")

        if not db_files:
            return None

        # 使用最新的数据库文件
        latest_db_file = max(db_files, key=os.path.getctime)
        db_path = latest_db_file[:-3]  # 移除 .db 后缀

        try:
            print(f"🔄 尝试加载已存在的数据库: {db_path}")

            # 检查是否是 FAISS 数据库
            if "faiss" in db_path.lower() and FAISS_AVAILABLE:
                vector_db = FAISSVectorDatabase(db_path)
                if vector_db._load_faiss_index():
                    print(f"✅ 成功加载 FAISS 数据库")
                    return vector_db
            else:
                vector_db = VectorDatabase(db_path)
                print(f"✅ 成功加载基础数据库")
                return vector_db

        except Exception as e:
            print(f"⚠️ 加载数据库失败: {e}")
            return None

        return None

    def process_new_trajectory(self, ship_name: str, trajectory_coords: List[tuple]):
        """
        处理新的航迹数据
        Args:
            ship_name: 舰船名称
            trajectory_coords: 12小时航迹坐标列表 [(lat, lon), ...]
        Returns:
            处理后的向量和元数据
        """
        print(f"\n🆕 处理新航迹数据:")
        print(f"  舰船名称: {ship_name}")
        print(f"  坐标点数: {len(trajectory_coords)}")

        # 第一步：坐标文本化 (与离线处理完全相同)
        # 转换坐标格式以匹配 coordinates_to_geohash 函数的期望格式
        coordinates = [{'lat': lat, 'lng': lon} for lat, lon in trajectory_coords]

        # 使用 coordinates_to_geohash 函数
        geohash_sentence = coordinates_to_geohash(coordinates, precision=7)
        print(f"  Geohash序列: {geohash_sentence}")

        # 第二步：创建身份文本 (与离线处理完全相同)
        identity_text = create_identity_text(ship_name, geohash_sentence)
        print(f"  身份文本: {identity_text}")

        # 第三步：文本向量化 (使用相同的模型)
        query_vector = text_to_vector(identity_text)
        print(f"  查询向量维度: {len(query_vector)}")

        return {
            'ship_name': ship_name,
            'trajectory_coords': trajectory_coords,
            'geohash_sentence': geohash_sentence,
            'identity_text': identity_text,
            'query_vector': query_vector,
            'coordinate_count': len(trajectory_coords)
        }

    def similarity_search_and_classify(self, query_data: Dict, vector_db, top_k: int = 5):
        """
        相似度搜索和分类决策
        Args:
            query_data: 处理后的查询数据
            vector_db: 向量数据库
            top_k: 返回最相似的前k个结果
        Returns:
            分类结果和详细信息
        """
        print(f"\n🔍 相似度搜索和分类决策:")
        print(f"  搜索Top-{top_k}最相似的历史片段...")

        query_vector = query_data['query_vector']

        try:
            # 使用向量数据库进行相似度搜索
            if isinstance(vector_db, FAISSVectorDatabase):
                # FAISS 搜索
                similar_results = vector_db.search_similar_vectors(
                    query_vector,
                    top_k=top_k
                )

                # 转换格式以统一处理
                similar_vectors = []
                for faiss_id, score, metadata in similar_results:
                    similar_vectors.append({
                        'id': faiss_id,
                        'similarity': score,
                        'action': metadata['action'],
                        'metadata': metadata
                    })
            else:
                # 基础数据库搜索
                similar_results = vector_db.find_similar_vectors(
                    query_vector,
                    top_k=top_k,
                    similarity_threshold=0.3
                )

                similar_vectors = []
                for vector_id, similarity, metadata in similar_results:
                    similar_vectors.append({
                        'id': vector_id,
                        'similarity': similarity,
                        'action': metadata['action'],
                        'metadata': metadata
                    })

            if not similar_vectors:
                return {
                    'predicted_action': '未知',
                    'confidence': 0.0,
                    'message': '未找到相似的历史样本',
                    'similar_samples': []
                }

            print(f"  找到 {len(similar_vectors)} 个相似样本")

            # 投票决策：统计各行为类型的得分
            action_votes = {}
            action_similarities = {}

            for sample in similar_vectors:
                action = sample['action']
                similarity = sample['similarity']

                if action not in action_votes:
                    action_votes[action] = 0
                    action_similarities[action] = []

                action_votes[action] += 1
                action_similarities[action].append(similarity)

            # 计算每种行为的综合得分（投票数 + 平均相似度）
            action_scores = {}
            for action in action_votes:
                vote_score = action_votes[action] / len(similar_vectors)  # 投票比例
                avg_similarity = np.mean(action_similarities[action])     # 平均相似度
                max_similarity = np.max(action_similarities[action])      # 最高相似度

                # 综合得分：投票权重0.4 + 平均相似度权重0.6
                combined_score = vote_score * 0.4 + avg_similarity * 0.6

                action_scores[action] = {
                    'vote_count': action_votes[action],
                    'vote_ratio': vote_score,
                    'avg_similarity': avg_similarity,
                    'max_similarity': max_similarity,
                    'combined_score': combined_score
                }

            # 确定最终预测结果
            best_action = max(action_scores.keys(), key=lambda x: action_scores[x]['combined_score'])
            confidence = action_scores[best_action]['combined_score']

            print(f"  🎯 预测结果: {best_action} (置信度: {confidence:.4f})")

            return {
                'predicted_action': best_action,
                'confidence': confidence,
                'action_scores': action_scores,
                'similar_samples': similar_vectors,
                'query_info': query_data
            }

        except Exception as e:
            print(f"❌ 相似度搜索失败: {e}")
            return {
                'predicted_action': '错误',
                'confidence': 0.0,
                'message': f'搜索失败: {e}',
                'similar_samples': []
            }

    def generate_explanation(self, result: Dict):
        """
        生成可解释的分类结果
        Args:
            result: 分类结果
        Returns:
            格式化的解释文本
        """
        if result['predicted_action'] in ['未知', '错误']:
            return f"❌ {result.get('message', '分类失败')}"

        explanation = []
        explanation.append(f"🎯 **预测行为**: {result['predicted_action']}")
        explanation.append(f"🔥 **置信度**: {result['confidence']:.1%}")

        # 判断依据
        explanation.append(f"\n📊 **判断依据**:")
        action_scores = result['action_scores']

        for action, scores in action_scores.items():
            explanation.append(f"  • {action}:")
            explanation.append(f"    - 投票数: {scores['vote_count']}/{len(result['similar_samples'])} ({scores['vote_ratio']:.1%})")
            explanation.append(f"    - 平均相似度: {scores['avg_similarity']:.1%}")
            explanation.append(f"    - 最高相似度: {scores['max_similarity']:.1%}")
            explanation.append(f"    - 综合得分: {scores['combined_score']:.1%}")

        # 最相似的历史样本
        explanation.append(f"\n🔍 **最相似的历史样本**:")
        similar_samples = result['similar_samples'][:3]  # 只显示前3个

        for i, sample in enumerate(similar_samples, 1):
            metadata = sample['metadata']
            explanation.append(f"  {i}. **{sample['action']}** (相似度: {sample['similarity']:.1%})")
            explanation.append(f"     - 向量维度: {metadata.get('vector_dim', 'Unknown')}")
            explanation.append(f"     - 坐标点数: {metadata.get('coordinate_count', 'Unknown')}")
            if 'time_range' in metadata:
                explanation.append(f"     - 时间范围: {metadata['time_range']}")

        # 查询信息
        query_info = result['query_info']
        explanation.append(f"\n📍 **查询航迹信息**:")
        explanation.append(f"  - 舰船名称: {query_info['ship_name']}")
        explanation.append(f"  - 坐标点数: {query_info['coordinate_count']}")
        explanation.append(f"  - 向量维度: {len(query_info['query_vector'])}")

        return '\n'.join(explanation)

    def online_inference(self, ship_name: str, trajectory_coords: List[tuple], top_k: int = 5):
        """
        完整的在线推理流程
        Args:
            ship_name: 舰船名称
            trajectory_coords: 12小时航迹坐标列表
            top_k: 返回最相似的前k个结果
        Returns:
            完整的分类结果和解释
        """
        print(f"\n🚀 开始在线推理流程")
        print(f"=" * 60)

        try:
            # 第一步：加载或构建舰船的向量数据库
            vector_db = self.load_or_build_ship_database(ship_name)

            if vector_db is None:
                return {
                    'success': False,
                    'message': f'无法获取 {ship_name} 的历史数据',
                    'predicted_action': '未知',
                    'confidence': 0.0
                }

            # 第二步：处理新航迹数据
            query_data = self.process_new_trajectory(ship_name, trajectory_coords)

            # 第三步：相似度搜索和分类决策
            result = self.similarity_search_and_classify(query_data, vector_db, top_k)

            # 第四步：生成可解释的结论
            explanation = self.generate_explanation(result)

            # 组装最终结果
            final_result = {
                'success': True,
                'ship_name': ship_name,
                'predicted_action': result['predicted_action'],
                'confidence': result['confidence'],
                'explanation': explanation,
                'detailed_result': result,
                'query_data': query_data
            }

            print(f"\n✅ 在线推理完成")
            return final_result

        except Exception as e:
            print(f"❌ 在线推理失败: {e}")
            import traceback
            traceback.print_exc()

            return {
                'success': False,
                'message': f'推理过程出错: {e}',
                'predicted_action': '错误',
                'confidence': 0.0
            }

# 全局在线推理引擎实例
_online_engine = None

def get_online_inference_engine():
    """获取全局在线推理引擎实例"""
    global _online_engine
    if _online_engine is None:
        _online_engine = OnlineInferenceEngine()
    return _online_engine

def online_trajectory_classification(ship_name: str, trajectory_coords: List[tuple], top_k: int = 5):
    """
    在线航迹行为分类的主要接口
    Args:
        ship_name: 舰船名称
        trajectory_coords: 12小时航迹坐标列表 [(lat, lon), ...]
        top_k: 返回最相似的前k个结果
    Returns:
        分类结果字典
    """
    engine = get_online_inference_engine()
    return engine.online_inference(ship_name, trajectory_coords, top_k)

def demo_online_inference():
    """演示在线推理功能"""
    print(f"\n🎬 阶段二：在线查询演示")
    print(f"=" * 60)

    # 示例1：已知舰船（文森号航空母舰）
    print(f"\n📋 示例1：已知舰船的新航迹分类")

    ship_name = "文森号航空母舰"
    # 模拟12小时的航迹数据
    trajectory_coords = [
        (35.1234, 139.5678),
        (35.1244, 139.5688),
        (35.1254, 139.5698),
        (35.1264, 139.5708),
        (35.1274, 139.5718),
        (35.1284, 139.5728),
        (35.1294, 139.5738),
        (35.1304, 139.5748)
    ]

    result1 = online_trajectory_classification(ship_name, trajectory_coords, top_k=5)

    if result1['success']:
        print(f"\n📊 分类结果:")
        print(result1['explanation'])
    else:
        print(f"❌ 分类失败: {result1['message']}")

    # 示例2：新舰船（需要在线获取历史数据）
    print(f"\n📋 示例2：新舰船的航迹分类")

    new_ship_name = "企业号航空母舰"  # 假设这是一个新的舰船
    # 模拟不同模式的航迹数据（停靠模式）
    trajectory_coords_2 = [
        (36.8485, -76.2951),  # 诺福克海军基地附近
        (36.8485, -76.2951),
        (36.8485, -76.2951),
        (36.8485, -76.2951),
        (36.8485, -76.2951),
        (36.8485, -76.2951)
    ]

    result2 = online_trajectory_classification(new_ship_name, trajectory_coords_2, top_k=3)

    if result2['success']:
        print(f"\n📊 分类结果:")
        print(result2['explanation'])
    else:
        print(f"❌ 分类失败: {result2['message']}")

    print(f"\n🎉 在线推理演示完成！")

def get_qwen_embedding_model():
    """
    获取或加载 Qwen3-Embedding 模型（单例模式）
    支持 GGUF 量化版本
    """
    global _embedding_model
    if _embedding_model is None:
        # 方法1: 尝试使用 llama-cpp-python 加载 GGUF 量化模型
        if LLAMA_CPP_AVAILABLE:
            try:
                print("正在使用 llama-cpp-python 加载 Qwen3-Embedding-0.6B-GGUF 模型...")
                # 注意：这里需要下载 GGUF 文件到本地
                # 可以从 Hugging Face 下载或使用本地路径
                model_path = "models/qwen3-embedding-0.6b.gguf"  # 本地路径

                # 尝试加载本地模型文件
                if os.path.exists(model_path):
                    llama_model = Llama(
                        model_path=model_path,
                        embedding=True,  # 启用嵌入模式
                        verbose=False
                    )
                    _embedding_model = {'model': llama_model, 'type': 'gguf'}
                    print("Qwen3-Embedding GGUF 模型加载完成")
                    return _embedding_model
                else:
                    print(f"GGUF 模型文件不存在: {model_path}")
                    print("请下载 Qwen3-Embedding-0.6B-GGUF 模型文件")
            except Exception as e:
                print(f"使用 llama-cpp-python 加载 GGUF 模型失败: {e}")

        # 方法2: 尝试使用 transformers 直接加载
        if TRANSFORMERS_AVAILABLE:
            try:
                print("正在使用 transformers 加载 Qwen3-Embedding-0.6B 模型...")
                tokenizer = AutoTokenizer.from_pretrained('Qwen/Qwen3-Embedding-0.6B')
                model = AutoModel.from_pretrained('Qwen/Qwen3-Embedding-0.6B')
                _embedding_model = {'tokenizer': tokenizer, 'model': model, 'type': 'transformers'}
                print("Qwen3-Embedding 模型加载完成 (transformers)")
                return _embedding_model
            except Exception as e:
                print(f"使用 transformers 加载 Qwen3-Embedding 模型失败: {e}")

        # 方法3: 尝试使用 sentence-transformers
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            try:
                print("正在使用 sentence-transformers 加载轻量级模型...")
                _embedding_model = {'model': SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2'), 'type': 'sentence_transformers'}
                print("轻量级模型加载完成 (sentence-transformers)")
                return _embedding_model
            except Exception as e:
                print(f"sentence-transformers 模型加载失败: {e}")

        # 方法4: 回退到哈希方法
        print("所有模型加载失败，使用简单哈希向量化方法")
        _embedding_model = {'type': 'hash'}

    return _embedding_model

def simple_hash_vectorizer(text, vector_dim=384):
    """
    简单的哈希向量化方法（备用方案）
    Args:
        text: 输入文本
        vector_dim: 向量维度
    Returns:
        np.ndarray: 向量表示
    """
    # 使用多个哈希函数生成向量的不同部分
    vector = np.zeros(vector_dim)

    # 将文本编码为字节
    text_bytes = text.encode('utf-8')

    # 使用不同的哈希算法生成向量的不同部分
    hash_funcs = [hashlib.md5, hashlib.sha1, hashlib.sha256]

    for i in range(vector_dim):
        # 选择哈希函数
        hash_func = hash_funcs[i % len(hash_funcs)]
        # 添加索引作为盐值
        salted_text = text_bytes + str(i).encode('utf-8')
        # 计算哈希值并转换为浮点数
        hash_value = int(hash_func(salted_text).hexdigest()[:8], 16)
        # 归一化到 [-1, 1] 范围
        vector[i] = (hash_value % 2000 - 1000) / 1000.0

    # L2 归一化
    norm = np.linalg.norm(vector)
    if norm > 0:
        vector = vector / norm

    return vector


def set_embedding_model_type(model_type="qwen"):
    """
    设置向量化模型类型
    Args:
        model_type: 模型类型，可选 "qwen", "sentence_transformer", "hash"
    """
    global _model_type
    _model_type = model_type
    print(f"向量化模型类型设置为: {model_type}")

def create_identity_text(ship_name, geohash_sentence):
    """
    第五步a：构建包含身份的输入文本
    将舰船名称与Geohash句子组合成信息丰富的输入字符串
    Args:
        ship_name: 舰船名称
        geohash_sentence: Geohash字符串序列
    Returns:
        str: 组合后的文本，格式为 "舰船名称 geohash序列"
    """
    if not geohash_sentence:
        return f"{ship_name}"
    return f"{ship_name} {geohash_sentence}"


def text_to_vector(text, vector_dim=None):
    """
    第五步c：文本向量化
    将组合好的文本字符串转换为固定维度的向量
    Args:
        text: 输入文本
        vector_dim: 向量维度（如果使用哈希方法）
    Returns:
        np.ndarray: 向量表示
    """
    model_info = get_qwen_embedding_model()

    if model_info['type'] == 'gguf':
        try:
            # 使用 GGUF 量化模型
            llama_model = model_info['model']

            # 使用 llama-cpp-python 生成嵌入向量
            embedding = llama_model.create_embedding(text)
            vector = np.array(embedding['data'][0]['embedding'])

            # 确保向量是一维的
            if vector.ndim > 1:
                vector = vector.flatten()

            # 只在每10个向量时显示一次进度，避免输出过多
            if not hasattr(text_to_vector, 'call_count'):
                text_to_vector.call_count = 0
            text_to_vector.call_count += 1

            if text_to_vector.call_count % 10 == 1:
                print(f"使用 Qwen3-Embedding GGUF 生成向量 (第 {text_to_vector.call_count} 个，维度: {len(vector)})")

            return vector

        except Exception as e:
            print(f"Qwen3-Embedding GGUF 向量化失败: {e}")

    elif model_info['type'] == 'transformers':
        try:
            # 使用 transformers 的 Qwen3-Embedding 模型
            tokenizer = model_info['tokenizer']
            model = model_info['model']

            # 编码文本
            inputs = tokenizer(text, return_tensors='pt', padding=True, truncation=True, max_length=512)

            with torch.no_grad():
                outputs = model(**inputs)
                # 使用 [CLS] token 的输出或者平均池化
                if hasattr(outputs, 'last_hidden_state'):
                    # 平均池化
                    vector = outputs.last_hidden_state.mean(dim=1).squeeze().numpy()
                else:
                    # 使用 pooler_output
                    vector = outputs.pooler_output.squeeze().numpy()

            print(f"使用 Qwen3-Embedding (transformers) 生成 {len(vector)} 维向量")
            return vector

        except Exception as e:
            print(f"Qwen3-Embedding (transformers) 向量化失败: {e}")

    elif model_info['type'] == 'sentence_transformers':
        try:
            # 使用 sentence-transformers 模型
            model = model_info['model']
            vector = model.encode(text, convert_to_numpy=True)
            print(f"使用 sentence-transformers 生成 {len(vector)} 维向量")
            return vector

        except Exception as e:
            print(f"sentence-transformers 向量化失败: {e}")

    # 回退到简单哈希向量化方法
    print("使用简单哈希向量化方法")
    if vector_dim is None:
        vector_dim = 384
    vector = simple_hash_vectorizer(text, vector_dim)
    return vector


def create_time_windows(block, ship_name, window_hours=12, stride_hours=12, geohash_precision=7, enable_vectorization=True):
    """
    第三步+第四步+第五步：为一个区块创建时间窗口、坐标文本化和向量化
    Args:
        block: 事件区块
        ship_name: 舰船名称
        window_hours: 窗口大小（小时）
        stride_hours: 步长（小时）
        geohash_precision: Geohash精度（6-8位）
        enable_vectorization: 是否启用向量化（默认True）
    """
    if not block['data_points']:
        return []

    start_time = parse_time(block['data_points'][0]['scout_time'])
    end_time = parse_time(block['data_points'][-1]['scout_time'])

    print(f"区块{block['block_id']} ({block['action']}):")
    print(f"  开始时间: {start_time}")
    print(f"  结束时间: {end_time}")
    print(f"  持续时间: {end_time - start_time}")

    # 计算可以创建多少个窗口
    total_hours = (end_time - start_time).total_seconds() / 3600
    windows_count = int((total_hours - window_hours) / stride_hours + 1)

    print(f"  总小时数: {total_hours:.2f} 小时")
    print(f"  窗口大小: {window_hours} 小时")
    print(f"  步长: {stride_hours} 小时")
    print(f"  可创建窗口数: {windows_count} 个")

    # 生成具体窗口
    windows = []
    for i in range(windows_count):
        window_start = start_time + timedelta(hours=i * stride_hours)
        window_end = window_start + timedelta(hours=window_hours)

        # 确保窗口不会超过区块的结束时间
        if window_end > end_time:
            window_end = end_time

        # 第四步：提取窗口内的坐标数据并转换为Geohash
        window_coordinates = extract_window_coordinates(block, window_start, window_end)
        geohash_sentence = coordinates_to_geohash(window_coordinates, precision=geohash_precision)

        # 第五步：文本向量化
        identity_text = None
        vector = None
        if enable_vectorization:
            # 第五步a：构建包含身份的输入文本
            identity_text = create_identity_text(ship_name, geohash_sentence)
            # 第五步c：生成向量
            vector = text_to_vector(identity_text)

        window = {
            'window_id': i + 1,
            'block_id': block['block_id'],
            'action': block['action'],
            'start_time': window_start,
            'end_time': window_end,
            'duration_hours': (window_end - window_start).total_seconds() / 3600,
            'coordinates': window_coordinates,  # 原始坐标数据
            'geohash_sentence': geohash_sentence,  # 第四步：Geohash文本化结果
            'coordinate_count': len(window_coordinates),  # 窗口内坐标点数量
            'identity_text': identity_text,  # 第五步a：包含身份的文本
            'vector': vector,  # 第五步c：向量表示
            'vector_dim': len(vector) if vector is not None else 0  # 向量维度
        }
        windows.append(window)

        # 调试信息：显示前3个窗口的详细信息
        if i < 3:
            print(f"    调试 - 窗口{i + 1}: {window_start} 到 {window_end}")
            print(f"      坐标点数量: {len(window_coordinates)}")
            if geohash_sentence:
                print(f"      Geohash序列: {geohash_sentence[:50]}{'...' if len(geohash_sentence) > 50 else ''}")
            else:
                print(f"      Geohash序列: (空)")
            if enable_vectorization and identity_text:
                print(f"      身份文本: {identity_text[:60]}{'...' if len(identity_text) > 60 else ''}")
                print(f"      向量维度: {window['vector_dim']}")

    print(f"  实际生成窗口数: {len(windows)} 个")
    print()

    return windows


if __name__ == "__main__":
    # 设置向量化模型类型（可选: "qwen", "sentence_transformer", "hash"）
    set_embedding_model_type("qwen")  # 使用 Qwen3-Embedding 模型

    ship_name = "文森号航空母舰"
    filtered_data = fetch_ship_data(ship_name, "1990-01-01", "2025-08-03")
    blocks = segment_by_action(filtered_data)

    print("=== 为所有区块生成时间窗口、坐标文本化和向量化 ===")
    all_windows = []
    total_windows_by_action = {}
    total_coordinates_by_action = {}
    total_vectors_by_action = {}

    # 重置向量化计数器
    if hasattr(text_to_vector, 'call_count'):
        text_to_vector.call_count = 0

    # 统计需要处理的区块数量
    valid_blocks = [block for block in blocks if block['size'] > 1]
    total_blocks = len(valid_blocks)
    print(f"需要处理 {total_blocks} 个有效区块")

    for i, block in enumerate(valid_blocks, 1):
        print(f"\n--- 处理区块 {i}/{total_blocks} (ID: {block['block_id']}, 行为: {block['action']}) ---")
        windows = create_time_windows(block, ship_name, window_hours=12, stride_hours=12,
                                    geohash_precision=7, enable_vectorization=True)
        all_windows.extend(windows)

        # 统计每种行为的窗口数量和坐标点数量
        action = block['action']
        if action not in total_windows_by_action:
            total_windows_by_action[action] = 0
            total_coordinates_by_action[action] = 0
            total_vectors_by_action[action] = 0

        total_windows_by_action[action] += len(windows)

        # 统计坐标点总数和向量数量
        for window in windows:
            total_coordinates_by_action[action] += window['coordinate_count']
            if window['vector'] is not None:
                total_vectors_by_action[action] += 1

    print("=== 统计结果 ===")
    print(f"总共生成了 {len(all_windows)} 个时间窗口")
    print("各行为类型的样本数量:")
    for action, count in total_windows_by_action.items():
        coord_count = total_coordinates_by_action[action]
        vector_count = total_vectors_by_action[action]
        avg_coords = coord_count / count if count > 0 else 0
        print(f"  {action}: {count} 个样本, {coord_count} 个坐标点 (平均每窗口 {avg_coords:.1f} 个点), {vector_count} 个向量")

    # 展示第四步的成果：显示几个Geohash文本化的例子
    print("\n=== 第四步成果展示：坐标文本化 (Geohash) 示例 ===")
    sample_count = 0
    for window in all_windows:
        if window['geohash_sentence'] and sample_count < 3:
            print(f"样本 {sample_count + 1}:")
            print(f"  行为: {window['action']}")
            print(f"  时间: {window['start_time']} 到 {window['end_time']}")
            print(f"  坐标点数: {window['coordinate_count']}")
            print(f"  Geohash序列: {window['geohash_sentence']}")
            print()
            sample_count += 1

    # 展示第五步的成果：显示几个文本向量化的例子
    print("\n=== 第五步成果展示：文本向量化 示例 ===")
    sample_count = 0
    for window in all_windows:
        if window['vector'] is not None and sample_count < 3:
            print(f"样本 {sample_count + 1}:")
            print(f"  行为: {window['action']}")
            print(f"  时间: {window['start_time']} 到 {window['end_time']}")
            print(f"  身份文本: {window['identity_text']}")
            print(f"  向量维度: {window['vector_dim']}")
            print(f"  向量前5个值: {window['vector'][:5].tolist()}")
            print(f"  向量范数: {np.linalg.norm(window['vector']):.4f}")
            print()
            sample_count += 1

    # 第六步：构建并存储向量数据库
    vector_db = step6_build_vector_database(all_windows, ship_name)

    # 演示向量数据库查询功能
    demo_vector_database_query(vector_db)

    # 演示新航迹分类
    print(f"\n=== 演示新航迹分类 ===")
    # 模拟一个新的航迹数据
    new_trajectory = [
        (35.1234, 139.5678),  # 示例坐标
        (35.1244, 139.5688),
        (35.1254, 139.5698),
        (35.1264, 139.5708),
        (35.1274, 139.5718)
    ]

    classification_result = classify_new_trajectory_with_database(
        vector_db, new_trajectory, ship_name
    )

    print(f"\n🎊 阶段一全部六步完成！")
    print(f"✅ 1. 数据解析和筛选")
    print(f"✅ 2. 按action进行事件分块")
    print(f"✅ 3. 固定长度滑窗切片")
    print(f"✅ 4. 坐标文本化 (Geohash)")
    print(f"✅ 5. 文本向量化")
    print(f"✅ 6. 构建并存储向量数据库")
    print(f"\n💡 现在可以使用向量数据库进行实时的航迹行为分类！")

    # 演示阶段二：在线查询
    print(f"\n" + "="*80)
    print(f"🚀 开始演示阶段二：在线查询 (Online Inference)")
    print(f"="*80)

    demo_online_inference()

    # #先看看第一个大区块的时间数据
    # print("=== 观察时间数据格式=== ")
    # big_block=None
    # for block in blocks:
    #     if block['size']>10:
    #         big_block=block
    #         break
    #
    # if big_block:
    #     print(f"区块{big_block['block_id']}:{big_block['action']}(共{big_block['size']}个点")
    #     print("前5个时间节点")
    #     for i,point in enumerate(big_block['data_point'][:5]):
    #         print(f"{i+1}.{point['scout_time']}")
    # block_json=json.dumps(blocks,ensure_ascii=False,indent=2)
    # print(block_json)

    # if filtered_data:
        # with open("ship_behavior_data.json",'w',encoding="UTF-8") as  f:
        #     json.dump(filtered_data,f,ensure_ascii=False,indent=2)
        #
        #     print(f"数据已经保存到ship_behavior_data.json,共{len(filtered_data)}条记录")
        # for item in filtered_data:
        #     # print(f"{item['action']} - {item['place_name']}")
        #     print(item)


        # print("===观察前10条数据action变化===")
        # for i,item in enumerate(filtered_data[:10]):
        #     print(f"第{i+1}条:{item['action']}-{item['scout_time']}")

        # print("=== 找出action变化的位置 ===")
        # #遍历数据，找到action变化的位置
        # for i in range(len(filtered_data)):
        #     # 如果是第一条数据，或者action发生变化
        #     current_action = filtered_data[i]["action"]
        #     if i==0 or filtered_data[i-1]["action"]!=current_action:
        #        print(f"位置{i}:开始新的‘{current_action}’区块")


        # print("=== 计算每个区块的范围 ===")
        #
        # #找出所有区块的起始位置
        # block_starts =[]
        # for i in range(len(filtered_data)):
        #     current_action = filtered_data[i]['action']
        #     if i==0 or filtered_data[i-1]['action']!=current_action:
        #         block_starts.append(i)
        #
        # #计算每个区块的范围和大小
        # for i in range(len(block_starts)):
        #     start_pos = block_starts[i]
        #
        #     #计算结束的位置
        #     if i==len(block_starts)-1:
        #         end_pos = len(filtered_data)-1
        #
        #     else:
        #         end_pos = block_starts[i+1]-1
        #
        #     #区块信息
        #     action = filtered_data[start_pos]['action']
        #     block_size = end_pos-start_pos+1
        #     print(f"区块{i+1}:{action}(位置{start_pos}-{end_pos},共{block_size}个点)")


