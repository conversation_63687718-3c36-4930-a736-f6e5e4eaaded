#!/usr/bin/env python3
"""
阶段一第六步：构建并存储向量数据库 (Build and Store Vector Database)
将前面生成的向量数据持久化存储，建立高效的检索索引
"""

import numpy as np
import json
import pickle
import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
import importlib.util

# 导入阶段一的函数
spec = importlib.util.spec_from_file_location("behavior_recognition", "src/behavior recognition.py")
behavior_recognition = importlib.util.module_from_spec(spec)
spec.loader.exec_module(behavior_recognition)

class VectorDatabase:
    """向量数据库类，用于存储和检索航迹行为向量"""
    
    def __init__(self, db_path: str = "vector_database"):
        self.db_path = db_path
        self.db_file = f"{db_path}.db"
        self.vectors_file = f"{db_path}_vectors.pkl"
        self.metadata_file = f"{db_path}_metadata.json"
        
        # 创建数据库目录
        os.makedirs(os.path.dirname(self.db_file) if os.path.dirname(self.db_file) else ".", exist_ok=True)
        
        # 初始化数据库
        self._init_database()
        
    def _init_database(self):
        """初始化SQLite数据库"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # 创建向量元数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS vector_metadata (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ship_name TEXT NOT NULL,
                action TEXT NOT NULL,
                identity_text TEXT,
                geohash_sentence TEXT,
                coordinate_count INTEGER,
                vector_dim INTEGER,
                vector_norm REAL,
                time_range TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                vector_id INTEGER UNIQUE
            )
        ''')
        
        # 创建行为统计表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS behavior_stats (
                action TEXT PRIMARY KEY,
                total_samples INTEGER,
                avg_vector_dim REAL,
                avg_coordinate_count REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_action ON vector_metadata(action)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_ship_name ON vector_metadata(ship_name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_vector_dim ON vector_metadata(vector_dim)')
        
        conn.commit()
        conn.close()
        
    def build_from_windows_data(self, windows_data: List[Dict], ship_name: str):
        """
        从时间窗口数据构建向量数据库
        Args:
            windows_data: 阶段一生成的时间窗口数据
            ship_name: 舰船名称
        """
        print(f"🔄 构建 {ship_name} 的向量数据库...")
        
        vectors = []
        metadata_list = []
        behavior_stats = {}
        
        valid_count = 0
        
        for i, window in enumerate(windows_data):
            if window['vector'] is not None:
                vector = window['vector']
                
                # 确保向量是一维的
                if vector.ndim > 1:
                    vector = vector.flatten()
                
                # 计算向量范数
                vector_norm = float(np.linalg.norm(vector))
                
                # 存储向量
                vectors.append(vector)
                
                # 存储元数据
                metadata = {
                    'vector_id': valid_count,
                    'ship_name': ship_name,
                    'action': window['action'],
                    'identity_text': window['identity_text'],
                    'geohash_sentence': window['geohash_sentence'],
                    'coordinate_count': window['coordinate_count'],
                    'vector_dim': len(vector),
                    'vector_norm': vector_norm,
                    'time_range': window.get('time_range', 'Unknown')
                }
                metadata_list.append(metadata)
                
                # 统计行为信息
                action = window['action']
                if action not in behavior_stats:
                    behavior_stats[action] = {
                        'count': 0,
                        'total_dim': 0,
                        'total_coords': 0
                    }
                
                behavior_stats[action]['count'] += 1
                behavior_stats[action]['total_dim'] += len(vector)
                behavior_stats[action]['total_coords'] += window['coordinate_count']
                
                valid_count += 1
        
        # 保存向量数据
        self._save_vectors(vectors)
        
        # 保存元数据到数据库
        self._save_metadata_to_db(metadata_list)
        
        # 保存行为统计
        self._save_behavior_stats(behavior_stats)
        
        # 保存元数据到JSON文件（备份）
        self._save_metadata_to_json(metadata_list, behavior_stats)
        
        print(f"✅ 向量数据库构建完成:")
        print(f"  - 总向量数: {len(vectors)}")
        print(f"  - 数据库文件: {self.db_file}")
        print(f"  - 向量文件: {self.vectors_file}")
        print(f"  - 元数据文件: {self.metadata_file}")
        
        return len(vectors)
    
    def _save_vectors(self, vectors: List[np.ndarray]):
        """保存向量数据到pickle文件"""
        with open(self.vectors_file, 'wb') as f:
            pickle.dump(vectors, f)
    
    def _save_metadata_to_db(self, metadata_list: List[Dict]):
        """保存元数据到SQLite数据库"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # 清空现有数据
        cursor.execute('DELETE FROM vector_metadata')
        
        # 插入新数据
        for metadata in metadata_list:
            cursor.execute('''
                INSERT INTO vector_metadata 
                (ship_name, action, identity_text, geohash_sentence, coordinate_count, 
                 vector_dim, vector_norm, time_range, vector_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                metadata['ship_name'],
                metadata['action'],
                metadata['identity_text'],
                metadata['geohash_sentence'],
                metadata['coordinate_count'],
                metadata['vector_dim'],
                metadata['vector_norm'],
                metadata['time_range'],
                metadata['vector_id']
            ))
        
        conn.commit()
        conn.close()
    
    def _save_behavior_stats(self, behavior_stats: Dict):
        """保存行为统计信息"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # 清空现有统计
        cursor.execute('DELETE FROM behavior_stats')
        
        # 插入新统计
        for action, stats in behavior_stats.items():
            avg_dim = stats['total_dim'] / stats['count']
            avg_coords = stats['total_coords'] / stats['count']
            
            cursor.execute('''
                INSERT INTO behavior_stats (action, total_samples, avg_vector_dim, avg_coordinate_count)
                VALUES (?, ?, ?, ?)
            ''', (action, stats['count'], avg_dim, avg_coords))
        
        conn.commit()
        conn.close()
    
    def _save_metadata_to_json(self, metadata_list: List[Dict], behavior_stats: Dict):
        """保存元数据到JSON文件（备份）"""
        data = {
            'created_at': datetime.now().isoformat(),
            'total_vectors': len(metadata_list),
            'behavior_stats': {
                action: {
                    'total_samples': stats['count'],
                    'avg_vector_dim': stats['total_dim'] / stats['count'],
                    'avg_coordinate_count': stats['total_coords'] / stats['count']
                }
                for action, stats in behavior_stats.items()
            },
            'metadata': metadata_list
        }
        
        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def load_vectors(self) -> List[np.ndarray]:
        """加载向量数据"""
        if not os.path.exists(self.vectors_file):
            raise FileNotFoundError(f"向量文件不存在: {self.vectors_file}")
        
        with open(self.vectors_file, 'rb') as f:
            return pickle.load(f)
    
    def query_metadata(self, action: Optional[str] = None, 
                      ship_name: Optional[str] = None,
                      min_vector_dim: Optional[int] = None,
                      max_vector_dim: Optional[int] = None) -> List[Dict]:
        """查询元数据"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        query = "SELECT * FROM vector_metadata WHERE 1=1"
        params = []
        
        if action:
            query += " AND action = ?"
            params.append(action)
        
        if ship_name:
            query += " AND ship_name = ?"
            params.append(ship_name)
        
        if min_vector_dim:
            query += " AND vector_dim >= ?"
            params.append(min_vector_dim)
        
        if max_vector_dim:
            query += " AND vector_dim <= ?"
            params.append(max_vector_dim)
        
        cursor.execute(query, params)
        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        conn.close()
        return results
    
    def get_behavior_stats(self) -> Dict:
        """获取行为统计信息"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM behavior_stats")
        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        conn.close()
        return {row['action']: row for row in results}
    
    def cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """计算余弦相似度"""
        # 处理不同维度的向量
        min_dim = min(len(vec1), len(vec2))
        v1 = vec1[:min_dim]
        v2 = vec2[:min_dim]
        
        # 计算余弦相似度
        dot_product = np.dot(v1, v2)
        norm1 = np.linalg.norm(v1)
        norm2 = np.linalg.norm(v2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    def find_similar_vectors(self, query_vector: np.ndarray, 
                           top_k: int = 5,
                           action_filter: Optional[str] = None,
                           similarity_threshold: float = 0.0) -> List[Tuple[int, float, Dict]]:
        """
        查找相似向量
        Args:
            query_vector: 查询向量
            top_k: 返回最相似的前k个结果
            action_filter: 行为类型过滤
            similarity_threshold: 相似度阈值
        Returns:
            List of (vector_id, similarity, metadata)
        """
        # 加载向量和元数据
        vectors = self.load_vectors()
        metadata_list = self.query_metadata(action=action_filter)
        
        # 计算相似度
        similarities = []
        
        for metadata in metadata_list:
            vector_id = metadata['vector_id']
            if vector_id < len(vectors):
                similarity = self.cosine_similarity(query_vector, vectors[vector_id])
                
                if similarity >= similarity_threshold:
                    similarities.append((vector_id, similarity, metadata))
        
        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        return similarities[:top_k]
    
    def get_database_info(self) -> Dict:
        """获取数据库信息"""
        info = {
            'database_files': {
                'db_file': self.db_file,
                'vectors_file': self.vectors_file,
                'metadata_file': self.metadata_file
            },
            'file_exists': {
                'db_file': os.path.exists(self.db_file),
                'vectors_file': os.path.exists(self.vectors_file),
                'metadata_file': os.path.exists(self.metadata_file)
            }
        }
        
        if info['file_exists']['db_file']:
            # 获取统计信息
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM vector_metadata")
            info['total_vectors'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT action) FROM vector_metadata")
            info['unique_actions'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT ship_name) FROM vector_metadata")
            info['unique_ships'] = cursor.fetchone()[0]
            
            conn.close()
        
        if info['file_exists']['vectors_file']:
            vectors = self.load_vectors()
            info['vector_file_size'] = len(vectors)
            if vectors:
                info['sample_vector_dim'] = len(vectors[0])
        
        return info

def build_vector_database_from_ship_data(ship_name: str = "文森号航空母舰", 
                                        db_name: str = None) -> VectorDatabase:
    """
    从舰船数据构建完整的向量数据库
    Args:
        ship_name: 舰船名称
        db_name: 数据库名称
    Returns:
        VectorDatabase实例
    """
    print(f"🚀 阶段一第六步：构建 {ship_name} 的向量数据库")
    print("=" * 60)
    
    # 设置数据库名称
    if not db_name:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        db_name = f"databases/vector_db_{ship_name}_{timestamp}"
    
    # 创建向量数据库实例
    vector_db = VectorDatabase(db_name)
    
    # 运行阶段一前五步，生成向量数据
    print("🔄 运行阶段一前五步...")
    
    # 设置向量化模型
    behavior_recognition.set_embedding_model_type("qwen")
    
    # 获取数据
    filtered_data = behavior_recognition.fetch_ship_data(ship_name, "1990-01-01", "2025-08-03")
    blocks = behavior_recognition.segment_by_action(filtered_data)
    
    # 生成时间窗口和向量化
    all_windows = []
    valid_blocks = [block for block in blocks if block['size'] > 1]
    
    print(f"处理 {len(valid_blocks)} 个行为区块...")
    
    for i, block in enumerate(valid_blocks, 1):
        if i % 5 == 1:  # 每5个区块显示一次进度
            print(f"  处理区块 {i}/{len(valid_blocks)}: {block['action']}")
        
        windows = behavior_recognition.create_time_windows(
            block, ship_name, 
            window_hours=12, stride_hours=12, 
            geohash_precision=7, enable_vectorization=True
        )
        all_windows.extend(windows)
    
    print(f"✅ 生成了 {len(all_windows)} 个时间窗口")
    
    # 构建向量数据库
    vector_count = vector_db.build_from_windows_data(all_windows, ship_name)
    
    # 显示数据库信息
    db_info = vector_db.get_database_info()
    behavior_stats = vector_db.get_behavior_stats()
    
    print(f"\n📊 向量数据库统计:")
    print(f"  - 总向量数: {db_info.get('total_vectors', 0)}")
    print(f"  - 行为类型数: {db_info.get('unique_actions', 0)}")
    print(f"  - 舰船数: {db_info.get('unique_ships', 0)}")
    
    print(f"\n📈 各行为类型统计:")
    for action, stats in behavior_stats.items():
        print(f"  {action}:")
        print(f"    样本数: {stats['total_samples']}")
        print(f"    平均向量维度: {stats['avg_vector_dim']:.1f}")
        print(f"    平均坐标点数: {stats['avg_coordinate_count']:.1f}")
    
    print(f"\n🎉 阶段一第六步完成！向量数据库已构建并存储")
    
    return vector_db

if __name__ == "__main__":
    # 演示构建向量数据库
    vector_db = build_vector_database_from_ship_data()
    
    # 演示查询功能
    print(f"\n🔍 演示查询功能:")
    
    # 查询停靠行为的向量
    docking_metadata = vector_db.query_metadata(action="停靠")
    print(f"停靠行为向量数: {len(docking_metadata)}")
    
    # 查询高维向量
    high_dim_metadata = vector_db.query_metadata(min_vector_dim=10000)
    print(f"高维向量(>=10000维)数: {len(high_dim_metadata)}")
