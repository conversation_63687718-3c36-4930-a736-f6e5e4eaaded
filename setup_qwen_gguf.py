#!/usr/bin/env python3
"""
下载和设置 Qwen3-Embedding-0.6B-GGUF 模型
"""

import os
import requests
from pathlib import Path
import hashlib

def download_file(url, local_path, chunk_size=8192):
    """下载文件并显示进度"""
    print(f"正在下载: {url}")
    print(f"保存到: {local_path}")
    
    response = requests.get(url, stream=True)
    response.raise_for_status()
    
    total_size = int(response.headers.get('content-length', 0))
    downloaded = 0
    
    os.makedirs(os.path.dirname(local_path), exist_ok=True)
    
    with open(local_path, 'wb') as f:
        for chunk in response.iter_content(chunk_size=chunk_size):
            if chunk:
                f.write(chunk)
                downloaded += len(chunk)
                if total_size > 0:
                    progress = (downloaded / total_size) * 100
                    print(f"\r进度: {progress:.1f}% ({downloaded}/{total_size} bytes)", end='')
    
    print(f"\n下载完成: {local_path}")

def setup_qwen_gguf_model():
    """设置 Qwen3-Embedding GGUF 模型"""
    
    # 创建模型目录
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    # GGUF 模型文件路径
    model_file = models_dir / "qwen3-embedding-0.6b.gguf"
    
    if model_file.exists():
        print(f"✅ 模型文件已存在: {model_file}")
        return str(model_file)
    
    print("🔍 正在查找 Qwen3-Embedding-0.6B-GGUF 模型...")
    
    # Hugging Face 上的 GGUF 模型 URL
    # 基于标准的 GGUF 文件命名约定
    possible_urls = [
        "https://huggingface.co/Qwen/Qwen3-Embedding-0.6B-GGUF/resolve/main/qwen3-embedding-0.6b.q4_0.gguf",
        "https://huggingface.co/Qwen/Qwen3-Embedding-0.6B-GGUF/resolve/main/qwen3-embedding-0.6b.q8_0.gguf",
        "https://huggingface.co/Qwen/Qwen3-Embedding-0.6B-GGUF/resolve/main/qwen3-embedding-0.6b.f16.gguf",
        "https://huggingface.co/Qwen/Qwen3-Embedding-0.6B-GGUF/resolve/main/model.gguf",
        "https://huggingface.co/Qwen/Qwen3-Embedding-0.6B-GGUF/resolve/main/qwen3-embedding.gguf",
        "https://huggingface.co/Qwen/Qwen3-Embedding-0.6B-GGUF/resolve/main/qwen3-embedding-0.6b.gguf"
    ]
    
    # 尝试下载模型文件
    for url in possible_urls:
        try:
            print(f"\n尝试下载: {url}")
            download_file(url, str(model_file))
            
            # 验证文件大小
            file_size = model_file.stat().st_size
            if file_size > 1024 * 1024:  # 至少1MB
                print(f"✅ 模型下载成功，文件大小: {file_size / (1024*1024):.1f} MB")
                return str(model_file)
            else:
                print(f"❌ 下载的文件太小，可能不是有效的模型文件")
                model_file.unlink()  # 删除无效文件
                
        except Exception as e:
            print(f"❌ 下载失败: {e}")
            if model_file.exists():
                model_file.unlink()
            continue
    
    print("\n❌ 无法自动下载 GGUF 模型文件")
    print("\n📋 手动下载步骤:")
    print("1. 访问: https://huggingface.co/Qwen/Qwen3-Embedding-0.6B-GGUF")
    print("2. 下载合适的 .gguf 文件 (推荐 q4_0 或 q8_0 版本)")
    print(f"3. 将文件重命名为: qwen3-embedding-0.6b.gguf")
    print(f"4. 放置到目录: {models_dir.absolute()}")
    
    return None

def test_gguf_model():
    """测试 GGUF 模型是否可用"""
    try:
        from llama_cpp import Llama
        
        model_path = "models/qwen3-embedding-0.6b.gguf"
        if not os.path.exists(model_path):
            print(f"❌ 模型文件不存在: {model_path}")
            return False
        
        print("🧪 正在测试 GGUF 模型...")
        
        # 加载模型
        llama_model = Llama(
            model_path=model_path,
            embedding=True,
            verbose=False
        )
        
        # 测试向量化
        test_text = "文森号航空母舰 xgzbrzp"
        embedding = llama_model.create_embedding(test_text)
        vector = embedding['data'][0]['embedding']
        
        print(f"✅ GGUF 模型测试成功!")
        print(f"✅ 向量维度: {len(vector)}")
        print(f"✅ 向量前5个值: {vector[:5]}")
        
        return True
        
    except ImportError:
        print("❌ llama-cpp-python 未安装")
        print("请运行: pip install llama-cpp-python")
        return False
    except Exception as e:
        print(f"❌ GGUF 模型测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=== Qwen3-Embedding GGUF 模型设置 ===")
    
    # 设置模型
    model_path = setup_qwen_gguf_model()
    
    if model_path:
        # 测试模型
        if test_gguf_model():
            print("\n🎉 Qwen3-Embedding GGUF 模型设置完成!")
            print(f"模型路径: {os.path.abspath(model_path)}")
        else:
            print("\n⚠️ 模型文件存在但测试失败")
    else:
        print("\n⚠️ 模型设置未完成，请手动下载模型文件")
