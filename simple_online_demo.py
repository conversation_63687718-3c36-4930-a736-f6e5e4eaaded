#!/usr/bin/env python3
"""
简化的阶段二在线查询演示
使用基础向量数据库进行演示
"""

import sys
import os
import importlib.util
import numpy as np
import pickle
import sqlite3
import json

# 导入主程序的函数
spec = importlib.util.spec_from_file_location("behavior_recognition", "src/behavior recognition.py")
behavior_recognition = importlib.util.module_from_spec(spec)
spec.loader.exec_module(behavior_recognition)

def load_basic_vector_database():
    """加载基础向量数据库"""
    print(f"🔄 加载基础向量数据库...")
    
    # 使用基础向量数据库
    db_path = "databases/vector_db_文森号航空母舰_20250805_175214"
    
    try:
        vector_db = behavior_recognition.VectorDatabase(db_path)
        
        # 检查文件是否存在
        db_info = vector_db.get_database_info()
        
        if db_info['file_exists']['db_file'] and db_info['file_exists']['vectors_file']:
            print(f"✅ 成功加载基础向量数据库")
            print(f"📊 数据库信息:")
            print(f"  - 总向量数: {db_info.get('total_vectors', 'Unknown')}")
            print(f"  - 向量文件大小: {db_info.get('vector_file_size', 'Unknown')}")
            
            return vector_db
        else:
            print(f"❌ 数据库文件不完整")
            return None
            
    except Exception as e:
        print(f"❌ 加载数据库失败: {e}")
        return None

def demo_online_classification():
    """演示在线分类"""
    print(f"\n🚀 阶段二：在线航迹行为分类演示")
    print(f"=" * 60)
    
    # 加载向量数据库
    vector_db = load_basic_vector_database()
    
    if not vector_db:
        print(f"❌ 无法加载向量数据库")
        return
    
    # 设置向量化模型
    behavior_recognition.set_embedding_model_type("qwen")
    
    ship_name = "文森号航空母舰"
    
    # 测试案例1：巡航模式（移动轨迹）
    print(f"\n📋 测试案例1：巡航模式航迹")
    trajectory_cruise = [
        (35.1234, 139.5678),
        (35.1244, 139.5688),
        (35.1254, 139.5698),
        (35.1264, 139.5708),
        (35.1274, 139.5718),
        (35.1284, 139.5728)
    ]
    
    result1 = classify_trajectory(vector_db, ship_name, trajectory_cruise, "巡航模式（移动轨迹）")
    
    # 测试案例2：停靠模式（固定位置）
    print(f"\n📋 测试案例2：停靠模式航迹")
    trajectory_docking = [
        (36.8485, -76.2951),  # 固定位置
        (36.8485, -76.2951),
        (36.8485, -76.2951),
        (36.8485, -76.2951),
        (36.8485, -76.2951),
        (36.8485, -76.2951)
    ]
    
    result2 = classify_trajectory(vector_db, ship_name, trajectory_docking, "停靠模式（固定位置）")
    
    # 测试案例3：航渡模式（长距离移动）
    print(f"\n📋 测试案例3：航渡模式航迹")
    trajectory_transit = [
        (35.0000, 139.0000),
        (35.5000, 139.5000),
        (36.0000, 140.0000),
        (36.5000, 140.5000),
        (37.0000, 141.0000),
        (37.5000, 141.5000)
    ]
    
    result3 = classify_trajectory(vector_db, ship_name, trajectory_transit, "航渡模式（长距离移动）")
    
    print(f"\n🎉 在线分类演示完成！")

def classify_trajectory(vector_db, ship_name, trajectory_coords, description):
    """
    对单个航迹进行分类
    Args:
        vector_db: 向量数据库
        ship_name: 舰船名称
        trajectory_coords: 航迹坐标
        description: 描述
    """
    print(f"\n🔍 分类航迹: {description}")
    print(f"  舰船: {ship_name}")
    print(f"  坐标点数: {len(trajectory_coords)}")
    
    try:
        # 第一步：坐标文本化
        # 转换坐标格式
        coordinates = [{'lat': lat, 'lng': lon} for lat, lon in trajectory_coords]

        # 使用 coordinates_to_geohash 函数
        geohash_list = behavior_recognition.coordinates_to_geohash(coordinates, precision=7)
        geohash_sentence = ' '.join(geohash_list)
        
        # 第二步：创建身份文本
        identity_text = behavior_recognition.create_identity_text(ship_name, geohash_sentence)
        print(f"  身份文本: {identity_text}")
        
        # 第三步：文本向量化
        query_vector = behavior_recognition.text_to_vector(identity_text)
        print(f"  查询向量维度: {len(query_vector)}")
        
        # 第四步：相似度搜索
        similar_vectors = vector_db.find_similar_vectors(
            query_vector, 
            top_k=5,
            similarity_threshold=0.3
        )
        
        if not similar_vectors:
            print(f"  ❌ 未找到相似的历史样本")
            return None
        
        print(f"  ✅ 找到 {len(similar_vectors)} 个相似样本")
        
        # 第五步：投票决策
        action_votes = {}
        action_similarities = {}
        
        for vector_id, similarity, metadata in similar_vectors:
            action = metadata['action']
            
            if action not in action_votes:
                action_votes[action] = 0
                action_similarities[action] = []
            
            action_votes[action] += 1
            action_similarities[action].append(similarity)
        
        # 计算综合得分
        action_scores = {}
        for action in action_votes:
            vote_ratio = action_votes[action] / len(similar_vectors)
            avg_similarity = np.mean(action_similarities[action])
            max_similarity = np.max(action_similarities[action])
            
            # 综合得分：投票权重0.4 + 平均相似度权重0.6
            combined_score = vote_ratio * 0.4 + avg_similarity * 0.6
            
            action_scores[action] = {
                'vote_count': action_votes[action],
                'vote_ratio': vote_ratio,
                'avg_similarity': avg_similarity,
                'max_similarity': max_similarity,
                'combined_score': combined_score
            }
        
        # 确定最终预测
        best_action = max(action_scores.keys(), key=lambda x: action_scores[x]['combined_score'])
        confidence = action_scores[best_action]['combined_score']
        
        # 输出结果
        print(f"\n  🎯 预测结果: {best_action} (置信度: {confidence:.1%})")
        
        print(f"  📊 详细得分:")
        for action, scores in action_scores.items():
            print(f"    • {action}:")
            print(f"      - 投票: {scores['vote_count']}/{len(similar_vectors)} ({scores['vote_ratio']:.1%})")
            print(f"      - 平均相似度: {scores['avg_similarity']:.1%}")
            print(f"      - 最高相似度: {scores['max_similarity']:.1%}")
            print(f"      - 综合得分: {scores['combined_score']:.1%}")
        
        print(f"  🔍 最相似的历史样本:")
        for i, (vector_id, similarity, metadata) in enumerate(similar_vectors[:3], 1):
            print(f"    {i}. {metadata['action']} (相似度: {similarity:.1%}, 维度: {metadata['vector_dim']})")
        
        return {
            'predicted_action': best_action,
            'confidence': confidence,
            'action_scores': action_scores,
            'similar_samples': similar_vectors
        }
        
    except Exception as e:
        print(f"  ❌ 分类失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print(f"🎬 简化的阶段二在线查询演示")
    print(f"=" * 80)
    
    # 检查数据库文件是否存在
    db_path = "databases/vector_db_文森号航空母舰_20250805_175214"
    
    if not os.path.exists(f"{db_path}.db"):
        print(f"❌ 未找到向量数据库文件")
        print(f"💡 请先运行主程序构建向量数据库:")
        print(f"   python \"src/behavior recognition.py\"")
        return
    
    # 运行演示
    demo_online_classification()

if __name__ == "__main__":
    main()
