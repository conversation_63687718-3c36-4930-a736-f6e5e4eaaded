#!/usr/bin/env python3
"""
简化版本的原版 Qwen3-Embedding 测试
解决分词器问题
"""

def test_original_qwen_simple():
    """测试原版 Qwen3-Embedding 模型"""
    print("=== 测试原版 Qwen3-Embedding 模型 ===")
    
    try:
        from transformers import AutoTokenizer, AutoModel
        import torch
        import time
        
        print("📥 正在加载原版模型...")
        start_time = time.time()
        
        # 尝试不同的模型名称
        model_names = [
            'Qwen/Qwen3-Embedding-0.6B',
            'Qwen/Qwen2.5-Embedding-0.6B',
            'sentence-transformers/all-MiniLM-L6-v2'  # 备选
        ]
        
        model = None
        tokenizer = None
        used_model_name = None
        
        for model_name in model_names:
            try:
                print(f"尝试加载: {model_name}")
                tokenizer = AutoTokenizer.from_pretrained(model_name)
                model = AutoModel.from_pretrained(model_name)
                used_model_name = model_name
                print(f"✅ 成功加载: {model_name}")
                break
            except Exception as e:
                print(f"❌ 加载失败: {model_name} - {e}")
                continue
        
        if model is None:
            print("❌ 所有模型都加载失败")
            return None
        
        load_time = time.time() - start_time
        print(f"✅ 模型加载完成，耗时: {load_time:.2f} 秒")
        
        # 测试文本
        test_texts = [
            "文森号航空母舰 xgzbrzp",
            "文森号航空母舰 xgzupup", 
            "其他舰船 abc123def"
        ]
        
        vectors = []
        total_inference_time = 0
        
        print("\n🧪 开始向量化测试...")
        for i, text in enumerate(test_texts, 1):
            print(f"处理文本 {i}: {text}")
            
            start_time = time.time()
            
            # 编码文本
            inputs = tokenizer(text, return_tensors='pt', padding=True, truncation=True, max_length=512)
            
            with torch.no_grad():
                outputs = model(**inputs)
                
                # 尝试不同的向量提取方法
                if hasattr(outputs, 'last_hidden_state'):
                    # 方法1: 平均池化
                    vector = outputs.last_hidden_state.mean(dim=1).squeeze().numpy()
                elif hasattr(outputs, 'pooler_output'):
                    # 方法2: pooler输出
                    vector = outputs.pooler_output.squeeze().numpy()
                else:
                    # 方法3: 使用第一个token
                    vector = outputs[0][:, 0, :].squeeze().numpy()
            
            inference_time = time.time() - start_time
            total_inference_time += inference_time
            
            vectors.append(vector)
            print(f"  ✅ 向量维度: {len(vector)}, 推理时间: {inference_time:.3f}s")
        
        print(f"\n📊 原版模型性能统计:")
        print(f"  使用模型: {used_model_name}")
        print(f"  平均推理时间: {total_inference_time/len(test_texts):.3f}s")
        print(f"  总推理时间: {total_inference_time:.3f}s")
        
        # 测试相似度
        print(f"\n🔍 相似度测试:")
        import numpy as np
        
        def cosine_similarity(a, b):
            return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))
        
        sim_1_2 = cosine_similarity(vectors[0], vectors[1])
        sim_1_3 = cosine_similarity(vectors[0], vectors[2])
        
        print(f"  相似文本相似度: {sim_1_2:.4f}")
        print(f"  不相似文本相似度: {sim_1_3:.4f}")
        
        if sim_1_2 > sim_1_3:
            print("  ✅ 语义理解正常")
        else:
            print("  ⚠️ 语义理解可能有问题")
        
        return vectors, used_model_name
        
    except ImportError as e:
        print(f"❌ 依赖库不可用: {e}")
        return None, None
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_gguf_comparison():
    """对比测试 GGUF 版本"""
    print("\n=== 对比测试 GGUF 版本 ===")
    
    try:
        from llama_cpp import Llama
        import os
        import time
        
        model_path = "models/qwen3-embedding-0.6b.gguf"
        if not os.path.exists(model_path):
            print(f"❌ GGUF 模型文件不存在: {model_path}")
            return None
        
        print("📥 正在加载 GGUF 模型...")
        start_time = time.time()
        
        llama_model = Llama(
            model_path=model_path,
            embedding=True,
            verbose=False
        )
        
        load_time = time.time() - start_time
        print(f"✅ GGUF 模型加载完成，耗时: {load_time:.2f} 秒")
        
        # 相同的测试文本
        test_texts = [
            "文森号航空母舰 xgzbrzp",
            "文森号航空母舰 xgzupup", 
            "其他舰船 abc123def"
        ]
        
        vectors = []
        total_inference_time = 0
        
        print("\n🧪 开始向量化测试...")
        for i, text in enumerate(test_texts, 1):
            print(f"处理文本 {i}: {text}")
            
            start_time = time.time()
            
            embedding = llama_model.create_embedding(text)
            vector = embedding['data'][0]['embedding']
            
            inference_time = time.time() - start_time
            total_inference_time += inference_time
            
            vectors.append(vector)
            print(f"  ✅ 向量维度: {len(vector)}, 推理时间: {inference_time:.3f}s")
        
        print(f"\n📊 GGUF 模型性能统计:")
        print(f"  平均推理时间: {total_inference_time/len(test_texts):.3f}s")
        print(f"  总推理时间: {total_inference_time:.3f}s")
        
        return vectors, "GGUF"
        
    except Exception as e:
        print(f"❌ GGUF 测试失败: {e}")
        return None, None

def main():
    """主测试函数"""
    print("🚀 Qwen3-Embedding 原版模型测试")
    print("=" * 50)
    
    # 测试原版模型
    original_vectors, original_model = test_original_qwen_simple()
    
    # 测试 GGUF 版本
    gguf_vectors, gguf_model = test_gguf_comparison()
    
    # 总结
    print(f"\n🎯 测试总结:")
    if original_vectors:
        print(f"  ✅ 原版模型: {original_model} - 可用")
    else:
        print(f"  ❌ 原版模型: 不可用")
    
    if gguf_vectors:
        print(f"  ✅ GGUF模型: 可用")
    else:
        print(f"  ❌ GGUF模型: 不可用")
    
    print(f"\n💡 建议:")
    if original_vectors and gguf_vectors:
        print(f"  - 两种模型都可用，可以根据需要选择")
        print(f"  - GGUF版本: 内存占用更少，速度更快")
        print(f"  - 原版模型: 可能精度更高，兼容性更好")
    elif gguf_vectors:
        print(f"  - 推荐使用 GGUF 量化版本")
    elif original_vectors:
        print(f"  - 推荐使用原版模型")
    else:
        print(f"  - 建议使用简单哈希向量化方法")

if __name__ == "__main__":
    main()
