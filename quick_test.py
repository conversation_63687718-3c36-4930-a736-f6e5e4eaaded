#!/usr/bin/env python3
"""
快速测试修复后的功能
"""

import sys
import os
import importlib.util

# 导入主程序
spec = importlib.util.spec_from_file_location("behavior_recognition", "src/behavior recognition.py")
behavior_recognition = importlib.util.module_from_spec(spec)
spec.loader.exec_module(behavior_recognition)

def test_coordinates_to_geohash_fix():
    """测试 coordinates_to_geohash 函数修复"""
    print("🧪 测试 coordinates_to_geohash 函数修复")
    print("=" * 40)
    
    # 测试数据
    coordinates = [
        {'lat': 35.1234, 'lng': 139.5678},
        {'lat': 35.1244, 'lng': 139.5688},
        {'lat': 35.1254, 'lng': 139.5698}
    ]
    
    try:
        # 测试函数调用
        geohash_sentence = behavior_recognition.coordinates_to_geohash(coordinates, precision=7)
        print(f"✅ 函数调用成功")
        print(f"输入坐标数: {len(coordinates)}")
        print(f"输出Geohash: {geohash_sentence}")
        
        # 验证输出格式
        geohash_parts = geohash_sentence.split()
        print(f"Geohash片段数: {len(geohash_parts)}")
        
        if len(geohash_parts) == len(coordinates):
            print(f"✅ 输出格式正确")
        else:
            print(f"❌ 输出格式错误")
            
        return True
        
    except Exception as e:
        print(f"❌ 函数调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_process_new_trajectory():
    """测试 process_new_trajectory 函数"""
    print(f"\n🧪 测试 process_new_trajectory 函数")
    print("=" * 40)
    
    # 设置向量化模型
    behavior_recognition.set_embedding_model_type("qwen")
    
    # 获取在线推理引擎
    engine = behavior_recognition.get_online_inference_engine()
    
    # 测试数据
    ship_name = "测试舰船"
    trajectory_coords = [
        (35.1234, 139.5678),
        (35.1244, 139.5688),
        (35.1254, 139.5698)
    ]
    
    try:
        # 测试函数调用
        result = engine.process_new_trajectory(ship_name, trajectory_coords)
        
        print(f"✅ process_new_trajectory 调用成功")
        print(f"舰船名称: {result['ship_name']}")
        print(f"坐标点数: {result['coordinate_count']}")
        print(f"身份文本: {result['identity_text']}")
        print(f"向量维度: {len(result['query_vector'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ process_new_trajectory 调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 快速测试修复后的功能")
    print("=" * 50)
    
    # 测试1：coordinates_to_geohash 函数
    test1_success = test_coordinates_to_geohash_fix()
    
    # 测试2：process_new_trajectory 函数
    test2_success = test_process_new_trajectory()
    
    # 总结
    print(f"\n📊 测试结果总结")
    print("=" * 30)
    print(f"coordinates_to_geohash: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"process_new_trajectory: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print(f"\n🎉 所有测试通过！修复成功！")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试被中断")
    except Exception as e:
        print(f"❌ 测试程序出错: {e}")
        import traceback
        traceback.print_exc()
