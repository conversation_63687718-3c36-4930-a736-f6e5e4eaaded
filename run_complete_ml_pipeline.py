#!/usr/bin/env python3
"""
完整的机器学习流水线
从阶段一数据生成到模型训练的完整流程
"""

import sys
import os
import importlib.util
import numpy as np

# 导入阶段一的函数
spec = importlib.util.spec_from_file_location("behavior_recognition", "src/behavior recognition.py")
behavior_recognition = importlib.util.module_from_spec(spec)
spec.loader.exec_module(behavior_recognition)

# 导入阶段二的训练器
from stage2_ml_training import BehaviorClassificationTrainer

def run_stage1_data_generation():
    """运行阶段一数据生成"""
    print("🔄 运行阶段一：数据生成和向量化")
    print("=" * 50)
    
    # 设置向量化模型
    behavior_recognition.set_embedding_model_type("qwen")
    
    # 获取数据
    ship_name = "文森号航空母舰"
    print(f"获取 {ship_name} 的数据...")
    filtered_data = behavior_recognition.fetch_ship_data(ship_name, "1990-01-01", "2025-08-03")
    
    # 分块
    print("进行事件分块...")
    blocks = behavior_recognition.segment_by_action(filtered_data)
    
    # 生成时间窗口和向量化
    print("生成时间窗口和向量化...")
    all_windows = []
    
    # 只处理前几个区块进行快速演示
    valid_blocks = [block for block in blocks if block['size'] > 1][:3]  # 限制为3个区块
    
    for i, block in enumerate(valid_blocks, 1):
        print(f"处理区块 {i}/{len(valid_blocks)}: {block['action']}")
        windows = behavior_recognition.create_time_windows(
            block, ship_name, 
            window_hours=12, stride_hours=12, 
            geohash_precision=7, enable_vectorization=True
        )
        all_windows.extend(windows)
        print(f"  生成了 {len(windows)} 个窗口")
    
    print(f"\n✅ 阶段一完成，总共生成 {len(all_windows)} 个时间窗口")
    return all_windows

def run_stage2_ml_training(windows_data):
    """运行阶段二机器学习训练"""
    print(f"\n🤖 运行阶段二：机器学习模型训练")
    print("=" * 50)
    
    # 创建训练器
    trainer = BehaviorClassificationTrainer()
    
    try:
        # 准备数据
        X, y = trainer.prepare_data_from_windows(windows_data)
        
        # 检查数据质量
        if len(X) < 10:
            print("⚠️ 样本数量太少，建议增加更多数据")
            return None
        
        # 检查类别分布
        unique_labels, counts = np.unique(y, return_counts=True)
        if len(unique_labels) < 2:
            print("⚠️ 只有一个类别，无法进行分类训练")
            return None
        
        # 分割和标准化数据
        X_train, X_test, y_train, y_test, y_train_orig, y_test_orig = trainer.split_and_scale_data(X, y)
        
        # 训练多种模型
        trainer.train_multiple_models(X_train, y_train)
        
        # 评估模型
        results = trainer.evaluate_models(X_test, y_test, y_test_orig)
        
        # 可视化结果
        try:
            # 需要传递测试数据给绘图函数
            global X_test_global, y_test_original_global
            X_test_global = X_test
            y_test_original_global = y_test_orig
            trainer.plot_results(results)
        except Exception as e:
            print(f"⚠️ 可视化失败: {e}")
        
        # 保存最佳模型
        model_path = trainer.save_best_model()
        
        return trainer, results
        
    except Exception as e:
        print(f"❌ 机器学习训练失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_results(trainer, results):
    """分析训练结果"""
    print(f"\n📊 训练结果分析")
    print("=" * 50)
    
    if not results:
        print("❌ 没有训练结果可分析")
        return
    
    print(f"🏆 最佳模型: {trainer.best_model}")
    print(f"🎯 最佳交叉验证分数: {trainer.best_score:.4f}")
    
    print(f"\n📈 所有模型性能:")
    for name, result in results.items():
        print(f"  {name}:")
        print(f"    测试准确率: {result['accuracy']:.4f}")
        print(f"    交叉验证准确率: {result['cv_score']:.4f}")
    
    # 给出建议
    print(f"\n💡 建议:")
    if trainer.best_score > 0.9:
        print("  ✅ 模型性能优秀，可以用于生产环境")
    elif trainer.best_score > 0.8:
        print("  ✅ 模型性能良好，建议收集更多数据进一步优化")
    elif trainer.best_score > 0.7:
        print("  ⚠️ 模型性能一般，建议:")
        print("     - 收集更多训练数据")
        print("     - 尝试特征工程")
        print("     - 调整模型超参数")
    else:
        print("  ❌ 模型性能较差，建议:")
        print("     - 检查数据质量")
        print("     - 重新设计特征")
        print("     - 尝试其他算法")

def demonstrate_prediction(trainer):
    """演示模型预测"""
    print(f"\n🔮 模型预测演示")
    print("=" * 50)
    
    if not trainer or not trainer.best_model:
        print("❌ 没有可用的训练模型")
        return
    
    print("这里可以添加新数据的预测演示")
    print("例如：给定新的航迹向量，预测行为类别")

def main():
    """主函数"""
    print("🚀 完整的航迹行为识别机器学习流水线")
    print("=" * 60)
    
    try:
        # 阶段一：数据生成
        windows_data = run_stage1_data_generation()
        
        if not windows_data:
            print("❌ 阶段一数据生成失败")
            return
        
        # 阶段二：机器学习训练
        trainer, results = run_stage2_ml_training(windows_data)
        
        if trainer and results:
            # 分析结果
            analyze_results(trainer, results)
            
            # 演示预测
            demonstrate_prediction(trainer)
            
            print(f"\n🎉 完整流水线执行成功！")
            print(f"📁 模型文件已保存到 models/ 目录")
            print(f"📊 结果图表已生成")
        else:
            print("❌ 阶段二机器学习训练失败")
            
    except Exception as e:
        print(f"❌ 流水线执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
