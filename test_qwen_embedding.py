#!/usr/bin/env python3
"""
测试 Qwen3-Embedding 模型加载和使用
"""

def test_qwen_embedding():
    """测试 Qwen3-Embedding 模型"""
    try:
        from sentence_transformers import SentenceTransformer
        print("正在测试 Qwen3-Embedding 模型...")
        
        # 尝试加载模型
        model = SentenceTransformer('Qwen/Qwen3-Embedding-0.6B')
        print("✅ Qwen3-Embedding 模型加载成功")
        
        # 测试向量化
        test_text = "文森号航空母舰 xgzbrzp"
        vector = model.encode(test_text, convert_to_numpy=True)
        print(f"✅ 向量化成功，维度: {len(vector)}")
        print(f"✅ 向量前5个值: {vector[:5].tolist()}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False

def test_transformers_approach():
    """测试使用 transformers 直接加载"""
    try:
        from transformers import AutoTokenizer, AutoModel
        import torch
        print("\n正在测试 transformers 方式...")
        
        # 加载模型
        tokenizer = AutoTokenizer.from_pretrained('Qwen/Qwen3-Embedding-0.6B')
        model = AutoModel.from_pretrained('Qwen/Qwen3-Embedding-0.6B')
        print("✅ transformers 方式加载成功")
        
        # 测试向量化
        test_text = "文森号航空母舰 xgzbrzp"
        inputs = tokenizer(test_text, return_tensors='pt', padding=True, truncation=True)
        
        with torch.no_grad():
            outputs = model(**inputs)
            vector = outputs.last_hidden_state.mean(dim=1).squeeze().numpy()
        
        print(f"✅ 向量化成功，维度: {len(vector)}")
        print(f"✅ 向量前5个值: {vector[:5].tolist()}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False

if __name__ == "__main__":
    print("=== Qwen3-Embedding 模型测试 ===")
    
    # 测试 sentence-transformers 方式
    success1 = test_qwen_embedding()
    
    # 测试 transformers 方式
    success2 = test_transformers_approach()
    
    if success1 or success2:
        print("\n🎉 至少一种方式可以使用 Qwen3-Embedding!")
    else:
        print("\n⚠️ 两种方式都无法使用，将使用简单哈希向量化方法")
