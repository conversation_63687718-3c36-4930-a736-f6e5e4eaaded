#!/usr/bin/env python3
"""
缓存优化演示 - 展示舰船历史数据的智能缓存机制
"""

import sys
import os
import time
import importlib.util

# 导入主程序
spec = importlib.util.spec_from_file_location("behavior_recognition", "src/behavior recognition.py")
behavior_recognition = importlib.util.module_from_spec(spec)
spec.loader.exec_module(behavior_recognition)

def demo_cache_optimization():
    """演示缓存优化效果"""
    
    print("🚀 舰船历史数据缓存优化演示")
    print("=" * 60)
    
    # 设置向量化模型
    behavior_recognition.set_embedding_model_type("qwen")
    
    # 获取在线推理引擎
    engine = behavior_recognition.get_online_inference_engine()
    
    ship_name = "文森号航空母舰"
    
    # 测试航迹数据
    test_trajectories = [
        {
            "name": "巡航轨迹1",
            "coords": [
                (35.1234, 139.5678),
                (35.1244, 139.5688),
                (35.1254, 139.5698),
                (35.1264, 139.5708)
            ]
        },
        {
            "name": "停靠轨迹",
            "coords": [
                (36.8485, -76.2951),
                (36.8485, -76.2951),
                (36.8485, -76.2951),
                (36.8485, -76.2951)
            ]
        },
        {
            "name": "巡航轨迹2",
            "coords": [
                (40.7128, -74.0060),
                (40.7138, -74.0070),
                (40.7148, -74.0080),
                (40.7158, -74.0090)
            ]
        }
    ]
    
    print(f"\n📋 测试场景：对同一舰船 '{ship_name}' 进行多次分类")
    print(f"预期：第一次需要构建数据库，后续直接使用缓存")
    print()
    
    # 执行多次分类测试
    for i, trajectory in enumerate(test_trajectories, 1):
        print(f"\n{'='*50}")
        print(f"🧪 测试 {i}: {trajectory['name']}")
        print(f"{'='*50}")
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行分类
        result = behavior_recognition.online_trajectory_classification(
            ship_name, 
            trajectory['coords'], 
            top_k=3
        )
        
        # 记录结束时间
        end_time = time.time()
        processing_time = end_time - start_time
        
        # 显示结果
        if result['success']:
            print(f"\n📊 分类结果:")
            print(f"  🎯 预测行为: {result['predicted_action']}")
            print(f"  📈 置信度: {result['confidence']:.1%}")
            print(f"  ⏱️ 处理时间: {processing_time:.2f} 秒")
            
            if i == 1:
                print(f"  💡 说明: 第一次分类，需要构建向量数据库")
            else:
                print(f"  💡 说明: 使用缓存数据库，处理速度显著提升")
        else:
            print(f"❌ 分类失败: {result['message']}")
            print(f"⏱️ 处理时间: {processing_time:.2f} 秒")
        
        # 添加分隔符
        if i < len(test_trajectories):
            print(f"\n⏳ 准备下一个测试...")
            time.sleep(1)
    
    print(f"\n🎉 缓存优化演示完成！")
    print(f"\n📝 总结:")
    print(f"  ✅ 第一次分类：需要从API获取历史数据并向量化（较慢）")
    print(f"  ✅ 后续分类：直接使用缓存的向量数据库（很快）")
    print(f"  ✅ 只有用户输入的新航迹需要实时向量化")

def demo_different_ships():
    """演示不同舰船的缓存机制"""
    
    print(f"\n🚢 不同舰船缓存演示")
    print("=" * 40)
    
    ships_and_coords = [
        ("文森号航空母舰", [(35.1, 139.5), (35.2, 139.6)]),
        ("企业号航空母舰", [(40.7, -74.0), (40.8, -74.1)]),
        ("文森号航空母舰", [(36.8, -76.3), (36.8, -76.3)])  # 重复舰船
    ]
    
    for i, (ship_name, coords) in enumerate(ships_and_coords, 1):
        print(f"\n🧪 测试 {i}: {ship_name}")
        
        start_time = time.time()
        
        result = behavior_recognition.online_trajectory_classification(
            ship_name, coords, top_k=3
        )
        
        processing_time = time.time() - start_time
        
        if result['success']:
            print(f"  结果: {result['predicted_action']} ({processing_time:.2f}s)")
            if ship_name == "文森号航空母舰" and i == 3:
                print(f"  💡 注意: 重复舰船使用缓存，速度很快")
        else:
            print(f"  失败: {result['message']} ({processing_time:.2f}s)")

def show_cache_status():
    """显示当前缓存状态"""
    
    print(f"\n📊 缓存状态检查")
    print("=" * 30)
    
    # 检查数据库文件
    import glob
    db_files = glob.glob("databases/*.db")
    
    if db_files:
        print(f"✅ 发现 {len(db_files)} 个已缓存的数据库文件:")
        for db_file in db_files:
            file_size = os.path.getsize(db_file) / 1024  # KB
            print(f"  📁 {os.path.basename(db_file)} ({file_size:.1f} KB)")
    else:
        print(f"❌ 未发现缓存的数据库文件")
    
    # 检查内存缓存
    engine = behavior_recognition.get_online_inference_engine()
    if engine.vector_databases:
        print(f"\n✅ 内存中缓存的舰船数据库:")
        for ship_name in engine.vector_databases.keys():
            print(f"  🚢 {ship_name}")
    else:
        print(f"\n❌ 内存中无缓存数据库")

if __name__ == "__main__":
    print("🌟 舰船历史数据缓存优化演示程序")
    print("展示智能缓存如何提升重复查询的性能")
    print()
    
    try:
        # 显示当前缓存状态
        show_cache_status()
        
        # 主要演示
        demo_cache_optimization()
        
        # 不同舰船演示
        demo_different_ships()
        
        # 最终缓存状态
        print(f"\n" + "="*60)
        show_cache_status()
        
    except KeyboardInterrupt:
        print("\n👋 演示被中断")
    except Exception as e:
        print(f"❌ 演示出错: {e}")
        import traceback
        traceback.print_exc()
