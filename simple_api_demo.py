#!/usr/bin/env python3
"""
简单API演示 - 一键式舰船航迹行为分类
展示如何使用一个函数调用完成整个分类流程
"""

import sys
import os
import importlib.util

# 导入主程序
spec = importlib.util.spec_from_file_location("behavior_recognition", "src/behavior recognition.py")
behavior_recognition = importlib.util.module_from_spec(spec)
spec.loader.exec_module(behavior_recognition)

def one_click_classify(ship_name, trajectory_coords):
    """
    一键式航迹行为分类
    
    Args:
        ship_name: 舰船名称
        trajectory_coords: 坐标列表 [(lat, lon), ...]
    
    Returns:
        分类结果
    """
    print(f"🚀 一键式分类：{ship_name}")
    print(f"📍 坐标点数：{len(trajectory_coords)}")
    
    # 设置模型
    behavior_recognition.set_embedding_model_type("qwen")
    
    # 执行分类
    result = behavior_recognition.online_trajectory_classification(
        ship_name, trajectory_coords, top_k=5
    )
    
    return result

def demo_examples():
    """演示几个例子"""
    
    print("🎯 舰船航迹行为分类演示")
    print("=" * 50)
    
    # 示例1：巡航行为（移动轨迹）
    print("\n📋 示例1：巡航行为测试")
    cruise_coords = [
        (35.1234, 139.5678),
        (35.1244, 139.5688), 
        (35.1254, 139.5698),
        (35.1264, 139.5708),
        (35.1274, 139.5718),
        (35.1284, 139.5728)
    ]
    
    result1 = one_click_classify("文森号航空母舰", cruise_coords)
    
    if result1['success']:
        print(f"✅ 预测结果：{result1['predicted_action']}")
        print(f"📊 置信度：{result1['confidence']:.1%}")
    else:
        print(f"❌ 分类失败：{result1['message']}")
    
    print("\n" + "-"*50)
    
    # 示例2：停靠行为（固定位置）
    print("\n📋 示例2：停靠行为测试")
    docking_coords = [
        (36.8485, -76.2951),  # 诺福克海军基地
        (36.8485, -76.2951),
        (36.8485, -76.2951),
        (36.8485, -76.2951),
        (36.8485, -76.2951)
    ]
    
    result2 = one_click_classify("文森号航空母舰", docking_coords)
    
    if result2['success']:
        print(f"✅ 预测结果：{result2['predicted_action']}")
        print(f"📊 置信度：{result2['confidence']:.1%}")
        
        # 显示详细解释
        print(f"\n📝 详细解释：")
        print(result2['explanation'])
    else:
        print(f"❌ 分类失败：{result2['message']}")

def custom_input_demo():
    """自定义输入演示"""
    print("\n🔧 自定义输入测试")
    print("=" * 30)
    
    # 获取用户输入
    ship_name = input("请输入舰船名称: ").strip()
    if not ship_name:
        ship_name = "文森号航空母舰"  # 默认值
        print(f"使用默认舰船名称: {ship_name}")
    
    print("\n请输入航迹坐标（格式：纬度,经度）")
    print("输入空行结束输入")
    
    coords = []
    while True:
        coord_str = input("坐标: ").strip()
        if not coord_str:
            break
        
        try:
            lat, lon = map(float, coord_str.split(','))
            coords.append((lat, lon))
            print(f"✅ 已添加: ({lat}, {lon})")
        except:
            print("❌ 格式错误，请使用 '纬度,经度' 格式")
    
    if len(coords) == 0:
        print("❌ 未输入任何坐标")
        return
    
    # 执行分类
    result = one_click_classify(ship_name, coords)
    
    if result['success']:
        print(f"\n🎯 分类结果:")
        print(f"预测行为: {result['predicted_action']}")
        print(f"置信度: {result['confidence']:.1%}")
        print(f"\n解释:\n{result['explanation']}")
    else:
        print(f"❌ 分类失败: {result['message']}")

if __name__ == "__main__":
    print("🌟 简单API演示程序")
    print("展示如何使用一个函数完成舰船航迹行为分类")
    print()
    
    try:
        # 运行预设示例
        demo_examples()
        
        # 询问是否进行自定义输入测试
        print("\n" + "="*50)
        custom_test = input("是否进行自定义输入测试？(y/n): ").strip().lower()
        if custom_test in ['y', 'yes', '是']:
            custom_input_demo()
        
        print("\n🎉 演示完成！")
        
    except KeyboardInterrupt:
        print("\n👋 程序被中断")
    except Exception as e:
        print(f"❌ 程序出错: {e}")
        import traceback
        traceback.print_exc()
