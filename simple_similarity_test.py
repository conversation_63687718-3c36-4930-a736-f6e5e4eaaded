#!/usr/bin/env python3
"""
简单的相似度修复测试 - 使用已有数据库
"""

import sys
import os
import importlib.util
import numpy as np

# 导入主程序
spec = importlib.util.spec_from_file_location("behavior_recognition", "src/behavior recognition.py")
behavior_recognition = importlib.util.module_from_spec(spec)
spec.loader.exec_module(behavior_recognition)

def test_distance_conversion():
    """测试距离转换函数"""
    print("🧮 测试FAISS距离到相似度转换")
    print("=" * 40)
    
    # 模拟FAISS返回的L2距离
    test_distances = [0.0, 0.1, 0.5, 1.0, 2.0, 5.0, 10.0]
    
    print("原始L2距离 -> 转换后相似度")
    print("-" * 30)
    
    for distance in test_distances:
        # 使用修复后的转换公式
        similarity = 1.0 / (1.0 + distance)
        print(f"{distance:>8.1f} -> {similarity:>8.4f} ({similarity:>6.1%})")
    
    print(f"\n✅ 转换公式验证:")
    print(f"  - 距离=0 -> 相似度=100% ✅")
    print(f"  - 距离=1 -> 相似度=50% ✅") 
    print(f"  - 距离=9 -> 相似度=10% ✅")
    print(f"  - 所有相似度都在0-1范围内 ✅")

def test_with_existing_database():
    """使用已存在的数据库进行测试"""
    print(f"\n🔍 使用已存在数据库测试")
    print("=" * 40)
    
    # 查找已存在的数据库
    import glob
    db_files = glob.glob("databases/*文森号航空母舰*.db")
    
    if not db_files:
        print("❌ 未找到已存在的数据库文件")
        return False
    
    # 使用最新的数据库
    latest_db_file = max(db_files, key=os.path.getctime)
    db_path = latest_db_file[:-3]  # 移除.db后缀
    
    print(f"📁 使用数据库: {os.path.basename(db_path)}")
    
    try:
        # 加载数据库
        if "faiss" in db_path.lower():
            vector_db = behavior_recognition.FAISSVectorDatabase(db_path)
            if not vector_db._load_faiss_index():
                print("❌ FAISS索引加载失败")
                return False
        else:
            vector_db = behavior_recognition.VectorDatabase(db_path)
        
        print("✅ 数据库加载成功")
        
        # 设置向量化模型
        behavior_recognition.set_embedding_model_type("qwen")
        
        # 创建测试查询向量
        test_text = "文森号航空母舰 xn5pzfn xn5pzfn xn5pzfr"
        query_vector = behavior_recognition.text_to_vector(test_text)
        
        print(f"📊 查询向量维度: {len(query_vector)}")
        
        # 进行相似度搜索
        if hasattr(vector_db, 'search_similar_vectors'):
            results = vector_db.search_similar_vectors(query_vector, top_k=3)
            
            print(f"\n🔍 搜索结果 (修复后):")
            print(f"{'ID':<8} {'相似度':<10} {'百分比':<10} {'行为':<8}")
            print("-" * 40)
            
            for faiss_id, similarity, metadata in results:
                print(f"{faiss_id:<8} {similarity:<10.4f} {similarity:<10.1%} {metadata['action']:<8}")
            
            # 验证相似度范围
            all_similarities = [sim for _, sim, _ in results]
            if all_similarities:
                min_sim = min(all_similarities)
                max_sim = max(all_similarities)
                
                print(f"\n📈 相似度范围检查:")
                print(f"  最小相似度: {min_sim:.4f} ({min_sim:.1%})")
                print(f"  最大相似度: {max_sim:.4f} ({max_sim:.1%})")
                
                if 0 <= min_sim <= 1 and 0 <= max_sim <= 1:
                    print(f"  ✅ 所有相似度都在正常范围 (0-1)")
                    return True
                else:
                    print(f"  ❌ 相似度超出正常范围")
                    return False
        else:
            print("❌ 数据库不支持相似度搜索")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 相似度修复验证测试")
    print("=" * 50)
    
    # 测试1：转换公式验证
    test_distance_conversion()
    
    # 测试2：使用已存在数据库测试
    success = test_with_existing_database()
    
    print(f"\n📊 测试结果:")
    if success:
        print(f"✅ 相似度修复成功！")
        print(f"  - FAISS距离正确转换为相似度")
        print(f"  - 所有数值都在0-1范围内")
        print(f"  - 相似度计算逻辑正常")
    else:
        print(f"❌ 测试未完全通过")
        print(f"  - 可能需要重新构建数据库")
        print(f"  - 或者检查其他相关代码")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试被中断")
    except Exception as e:
        print(f"❌ 测试程序出错: {e}")
        import traceback
        traceback.print_exc()
