#!/usr/bin/env python3
"""
详细分析 Qwen3-Embedding 原版 vs GGUF 版本的兼容性问题
"""

import sys
import os

def check_transformers_version():
    """检查 transformers 版本和支持的模型"""
    print("=== Transformers 版本和兼容性检查 ===")
    
    try:
        import transformers
        print(f"✅ transformers 版本: {transformers.__version__}")
        
        # 检查可用的分词器类
        from transformers import AutoTokenizer
        from transformers.models.auto.tokenization_auto import TOKENIZER_MAPPING_NAMES
        
        print(f"\n📋 支持的分词器类型数量: {len(TOKENIZER_MAPPING_NAMES)}")
        
        # 查找 Qwen 相关的分词器
        qwen_tokenizers = [name for name in TOKENIZER_MAPPING_NAMES.values() if 'qwen' in name.lower()]
        print(f"🔍 Qwen 相关分词器: {qwen_tokenizers}")
        
        # 尝试导入 Qwen2Tokenizer
        try:
            from transformers import Qwen2Tokenizer
            print("✅ Qwen2Tokenizer 可以导入")
        except ImportError as e:
            print(f"❌ Qwen2Tokenizer 导入失败: {e}")
            
        # 检查是否有其他 Qwen 分词器
        try:
            from transformers.models.qwen2 import Qwen2Tokenizer as Qwen2TokenizerDirect
            print("✅ 直接导入 Qwen2Tokenizer 成功")
        except ImportError as e:
            print(f"❌ 直接导入 Qwen2Tokenizer 失败: {e}")
            
    except Exception as e:
        print(f"❌ transformers 检查失败: {e}")

def check_model_repository():
    """检查模型仓库信息"""
    print("\n=== 模型仓库信息检查 ===")
    
    try:
        from huggingface_hub import model_info, list_repo_files
        
        repo_id = "Qwen/Qwen3-Embedding-0.6B"
        print(f"🔍 检查仓库: {repo_id}")
        
        # 获取模型信息
        try:
            info = model_info(repo_id)
            print(f"✅ 仓库存在")
            print(f"📊 模型信息:")
            print(f"  - 模型ID: {info.modelId}")
            print(f"  - 标签: {info.tags}")
            if hasattr(info, 'config') and info.config:
                print(f"  - 架构: {info.config.get('architectures', 'Unknown')}")
                print(f"  - 模型类型: {info.config.get('model_type', 'Unknown')}")
        except Exception as e:
            print(f"❌ 获取模型信息失败: {e}")
        
        # 列出文件
        try:
            files = list_repo_files(repo_id)
            config_files = [f for f in files if 'config' in f.lower()]
            tokenizer_files = [f for f in files if 'tokenizer' in f.lower()]
            
            print(f"📁 配置文件: {config_files}")
            print(f"📁 分词器文件: {tokenizer_files}")
            
        except Exception as e:
            print(f"❌ 列出文件失败: {e}")
            
    except Exception as e:
        print(f"❌ 仓库检查失败: {e}")

def check_gguf_vs_original():
    """对比 GGUF 和原版的技术差异"""
    print("\n=== GGUF vs 原版技术差异分析 ===")
    
    print("🔧 GGUF 量化版本特点:")
    print("  - 使用 llama.cpp 框架")
    print("  - 自包含的模型文件（包含分词器信息）")
    print("  - 不依赖 transformers 库的分词器类")
    print("  - 直接从模型文件读取配置")
    
    print("\n🔧 原版模型特点:")
    print("  - 使用 transformers 框架")
    print("  - 需要匹配的分词器类")
    print("  - 依赖 transformers 版本兼容性")
    print("  - 需要正确的模型配置映射")

def test_manual_tokenizer_loading():
    """手动测试分词器加载"""
    print("\n=== 手动分词器加载测试 ===")
    
    try:
        from transformers import AutoConfig
        
        repo_id = "Qwen/Qwen3-Embedding-0.6B"
        
        # 尝试加载配置
        try:
            config = AutoConfig.from_pretrained(repo_id)
            print(f"✅ 配置加载成功")
            print(f"📋 模型类型: {config.model_type}")
            print(f"📋 架构: {config.architectures}")
            
            # 检查分词器类配置
            if hasattr(config, 'tokenizer_class'):
                print(f"📋 指定的分词器类: {config.tokenizer_class}")
            else:
                print("⚠️ 配置中没有指定分词器类")
                
        except Exception as e:
            print(f"❌ 配置加载失败: {e}")
            
        # 尝试不同的分词器加载方法
        print(f"\n🧪 尝试不同的分词器加载方法:")
        
        methods = [
            ("AutoTokenizer.from_pretrained", lambda: __import__('transformers').AutoTokenizer.from_pretrained(repo_id)),
            ("手动指定 Qwen2Tokenizer", lambda: __import__('transformers').Qwen2Tokenizer.from_pretrained(repo_id)),
        ]
        
        for method_name, method_func in methods:
            try:
                tokenizer = method_func()
                print(f"  ✅ {method_name}: 成功")
                print(f"     分词器类型: {type(tokenizer).__name__}")
                break
            except Exception as e:
                print(f"  ❌ {method_name}: {e}")
                
    except Exception as e:
        print(f"❌ 手动测试失败: {e}")

def check_dependency_versions():
    """检查相关依赖版本"""
    print("\n=== 依赖版本检查 ===")
    
    dependencies = [
        'transformers',
        'tokenizers', 
        'huggingface_hub',
        'torch',
        'numpy',
        'llama_cpp'
    ]
    
    for dep in dependencies:
        try:
            module = __import__(dep)
            version = getattr(module, '__version__', 'Unknown')
            print(f"✅ {dep}: {version}")
        except ImportError:
            print(f"❌ {dep}: 未安装")
        except Exception as e:
            print(f"⚠️ {dep}: 检查失败 - {e}")

def suggest_solutions():
    """提供解决方案建议"""
    print("\n=== 解决方案建议 ===")
    
    print("🔧 可能的解决方案:")
    print("1. 升级 transformers 到最新版本:")
    print("   pip install transformers>=4.40.0")
    
    print("\n2. 安装 Qwen 专用依赖:")
    print("   pip install transformers_stream_generator")
    
    print("\n3. 使用兼容的模型名称:")
    print("   - Qwen/Qwen2-0.5B")
    print("   - Qwen/Qwen2.5-0.5B")
    
    print("\n4. 手动下载并本地加载:")
    print("   - 下载模型文件到本地")
    print("   - 使用本地路径加载")
    
    print("\n5. 继续使用 GGUF 版本（推荐）:")
    print("   - 性能更好")
    print("   - 兼容性更强")
    print("   - 内存占用更少")

def main():
    """主函数"""
    print("🔍 Qwen3-Embedding 兼容性问题详细分析")
    print("=" * 60)
    
    check_transformers_version()
    check_model_repository()
    check_gguf_vs_original()
    test_manual_tokenizer_loading()
    check_dependency_versions()
    suggest_solutions()
    
    print(f"\n🎯 结论:")
    print("GGUF 版本可用而原版不可用的主要原因:")
    print("1. transformers 版本与 Qwen2Tokenizer 不兼容")
    print("2. GGUF 版本使用独立的 llama.cpp 框架，不依赖 transformers")
    print("3. GGUF 文件是自包含的，包含了所有必要的配置信息")
    print("4. 原版模型需要精确的版本匹配和依赖配置")

if __name__ == "__main__":
    main()
