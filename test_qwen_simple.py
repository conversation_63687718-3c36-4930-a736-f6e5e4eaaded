#!/usr/bin/env python3
"""
简化版本的 Qwen3-Embedding 测试
只处理少量数据，避免大量重复输出
"""

import sys
import os
sys.path.append('src')

# 导入主程序的函数
import importlib.util
spec = importlib.util.spec_from_file_location("behavior_recognition", "src/behavior recognition.py")
behavior_recognition = importlib.util.module_from_spec(spec)
spec.loader.exec_module(behavior_recognition)

# 导入需要的函数
fetch_ship_data = behavior_recognition.fetch_ship_data
segment_by_action = behavior_recognition.segment_by_action
create_time_windows = behavior_recognition.create_time_windows
set_embedding_model_type = behavior_recognition.set_embedding_model_type
text_to_vector = behavior_recognition.text_to_vector

def test_qwen_with_limited_data():
    """测试 Qwen3-Embedding，只处理少量数据"""
    
    print("=== 简化版 Qwen3-Embedding 测试 ===\n")
    
    # 设置向量化模型
    set_embedding_model_type("qwen")
    
    # 获取数据
    ship_name = "文森号航空母舰"
    print(f"获取 {ship_name} 的数据...")
    filtered_data = fetch_ship_data(ship_name, "1990-01-01", "2025-08-03")
    
    # 分块
    print("进行事件分块...")
    blocks = segment_by_action(filtered_data)
    
    # 只处理前2个有效区块，避免大量输出
    valid_blocks = [block for block in blocks if block['size'] > 1][:2]
    
    print(f"\n=== 处理前 {len(valid_blocks)} 个区块（简化测试） ===")
    
    all_windows = []
    for i, block in enumerate(valid_blocks, 1):
        print(f"\n--- 区块 {i}: {block['action']} (ID: {block['block_id']}) ---")
        print(f"数据点数量: {block['size']}")
        
        # 只生成前3个时间窗口
        windows = create_time_windows(
            block, ship_name, 
            window_hours=12, stride_hours=12, 
            geohash_precision=7, enable_vectorization=True
        )
        
        # 只取前3个窗口进行测试
        test_windows = windows[:3]
        all_windows.extend(test_windows)
        
        print(f"生成了 {len(test_windows)} 个测试窗口（实际可生成 {len(windows)} 个）")
        
        # 显示向量化结果
        for j, window in enumerate(test_windows, 1):
            if window['vector'] is not None:
                print(f"  窗口 {j}:")
                print(f"    身份文本: {window['identity_text']}")
                print(f"    向量维度: {window['vector_dim']}")
                print(f"    向量范数: {window['vector'].sum():.4f}")
    
    print(f"\n=== 测试完成 ===")
    print(f"总共测试了 {len(all_windows)} 个时间窗口")
    print("✅ Qwen3-Embedding GGUF 模型工作正常")
    
    return all_windows

def test_vector_similarity():
    """测试向量相似度"""
    print("\n=== 向量相似度测试 ===")
    
    # 测试相似和不相似的文本
    texts = [
        "文森号航空母舰 xgzbrzp",  # 相似文本1
        "文森号航空母舰 xgzupup",  # 相似文本2  
        "其他舰船 abc123def"        # 不相似文本
    ]
    
    vectors = []
    for i, text in enumerate(texts, 1):
        print(f"生成向量 {i}: {text}")
        vector = text_to_vector(text)
        vectors.append(vector)
        print(f"  维度: {len(vector)}")
    
    # 计算余弦相似度
    import numpy as np
    
    def cosine_similarity(a, b):
        # 确保向量维度一致
        min_dim = min(len(a), len(b))
        a_norm = a[:min_dim]
        b_norm = b[:min_dim]
        return np.dot(a_norm, b_norm) / (np.linalg.norm(a_norm) * np.linalg.norm(b_norm))
    
    sim_1_2 = cosine_similarity(vectors[0], vectors[1])
    sim_1_3 = cosine_similarity(vectors[0], vectors[2])
    
    print(f"\n相似度结果:")
    print(f"  文本1 vs 文本2: {sim_1_2:.4f}")
    print(f"  文本1 vs 文本3: {sim_1_3:.4f}")
    
    if sim_1_2 > sim_1_3:
        print("✅ 相似度测试通过：相关文本更相似")
    else:
        print("⚠️ 相似度测试异常")

if __name__ == "__main__":
    try:
        # 主测试
        windows = test_qwen_with_limited_data()
        
        # 相似度测试
        if windows:
            test_vector_similarity()
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
