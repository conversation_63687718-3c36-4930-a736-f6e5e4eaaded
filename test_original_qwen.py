#!/usr/bin/env python3
"""
测试 Qwen3-Embedding 原版模型 vs GGUF 量化版本
对比两种模型的效果和性能
"""

import time
import numpy as np
import os

def test_transformers_original():
    """测试 transformers 原版 Qwen3-Embedding 模型"""
    print("=== 测试 Transformers 原版 Qwen3-Embedding ===")
    
    try:
        from transformers import AutoTokenizer, AutoModel
        import torch
        
        print("📥 正在下载/加载 Qwen3-Embedding-0.6B 原版模型...")
        start_time = time.time()
        
        # 加载模型和分词器
        tokenizer = AutoTokenizer.from_pretrained('Qwen/Qwen3-Embedding-0.6B')
        model = AutoModel.from_pretrained('Qwen/Qwen3-Embedding-0.6B')
        
        load_time = time.time() - start_time
        print(f"✅ 原版模型加载完成，耗时: {load_time:.2f} 秒")
        
        # 测试文本
        test_texts = [
            "文森号航空母舰 xgzbrzp",
            "文森号航空母舰 xgzupup", 
            "其他舰船 abc123def",
            "文森号航空母舰 停靠港口",
            "巡航任务执行中"
        ]
        
        vectors = []
        total_inference_time = 0
        
        print("\n🧪 开始向量化测试...")
        for i, text in enumerate(test_texts, 1):
            print(f"处理文本 {i}: {text}")
            
            start_time = time.time()
            
            # 编码文本
            inputs = tokenizer(text, return_tensors='pt', padding=True, truncation=True, max_length=512)
            
            with torch.no_grad():
                outputs = model(**inputs)
                # 使用平均池化
                if hasattr(outputs, 'last_hidden_state'):
                    vector = outputs.last_hidden_state.mean(dim=1).squeeze().numpy()
                else:
                    vector = outputs.pooler_output.squeeze().numpy()
            
            inference_time = time.time() - start_time
            total_inference_time += inference_time
            
            vectors.append(vector)
            print(f"  ✅ 向量维度: {len(vector)}, 推理时间: {inference_time:.3f}s")
        
        print(f"\n📊 原版模型性能统计:")
        print(f"  平均推理时间: {total_inference_time/len(test_texts):.3f}s")
        print(f"  总推理时间: {total_inference_time:.3f}s")
        
        return vectors, "transformers"
        
    except ImportError as e:
        print(f"❌ transformers 库不可用: {e}")
        return None, None
    except Exception as e:
        print(f"❌ 原版模型加载失败: {e}")
        return None, None

def test_gguf_quantized():
    """测试 GGUF 量化版本"""
    print("\n=== 测试 GGUF 量化版本 ===")
    
    try:
        from llama_cpp import Llama
        
        model_path = "models/qwen3-embedding-0.6b.gguf"
        if not os.path.exists(model_path):
            print(f"❌ GGUF 模型文件不存在: {model_path}")
            return None, None
        
        print("📥 正在加载 GGUF 量化模型...")
        start_time = time.time()
        
        llama_model = Llama(
            model_path=model_path,
            embedding=True,
            verbose=False
        )
        
        load_time = time.time() - start_time
        print(f"✅ GGUF 模型加载完成，耗时: {load_time:.2f} 秒")
        
        # 测试文本（与原版相同）
        test_texts = [
            "文森号航空母舰 xgzbrzp",
            "文森号航空母舰 xgzupup", 
            "其他舰船 abc123def",
            "文森号航空母舰 停靠港口",
            "巡航任务执行中"
        ]
        
        vectors = []
        total_inference_time = 0
        
        print("\n🧪 开始向量化测试...")
        for i, text in enumerate(test_texts, 1):
            print(f"处理文本 {i}: {text}")
            
            start_time = time.time()
            
            # 生成嵌入向量
            embedding = llama_model.create_embedding(text)
            vector = np.array(embedding['data'][0]['embedding'])
            
            # 确保向量是一维的
            if vector.ndim > 1:
                vector = vector.flatten()
            
            inference_time = time.time() - start_time
            total_inference_time += inference_time
            
            vectors.append(vector)
            print(f"  ✅ 向量维度: {len(vector)}, 推理时间: {inference_time:.3f}s")
        
        print(f"\n📊 GGUF 模型性能统计:")
        print(f"  平均推理时间: {total_inference_time/len(test_texts):.3f}s")
        print(f"  总推理时间: {total_inference_time:.3f}s")
        
        return vectors, "gguf"
        
    except ImportError as e:
        print(f"❌ llama-cpp-python 库不可用: {e}")
        return None, None
    except Exception as e:
        print(f"❌ GGUF 模型加载失败: {e}")
        return None, None

def compare_vectors(vectors1, vectors2, model1_name, model2_name):
    """对比两种模型的向量效果"""
    print(f"\n=== 对比 {model1_name} vs {model2_name} ===")
    
    if vectors1 is None or vectors2 is None:
        print("❌ 无法进行对比，某个模型未成功加载")
        return
    
    def cosine_similarity(a, b):
        # 处理不同维度的向量
        min_dim = min(len(a), len(b))
        a_norm = a[:min_dim]
        b_norm = b[:min_dim]
        return np.dot(a_norm, b_norm) / (np.linalg.norm(a_norm) * np.linalg.norm(b_norm))
    
    print("📊 相似度对比:")
    print("文本对比: '文森号航空母舰 xgzbrzp' vs '文森号航空母舰 xgzupup'")
    
    # 计算相似文本的相似度
    sim1_model1 = cosine_similarity(vectors1[0], vectors1[1])
    sim1_model2 = cosine_similarity(vectors2[0], vectors2[1])
    
    print(f"  {model1_name}: {sim1_model1:.4f}")
    print(f"  {model2_name}: {sim1_model2:.4f}")
    
    print("\n文本对比: '文森号航空母舰 xgzbrzp' vs '其他舰船 abc123def'")
    
    # 计算不相似文本的相似度
    sim2_model1 = cosine_similarity(vectors1[0], vectors1[2])
    sim2_model2 = cosine_similarity(vectors2[0], vectors2[2])
    
    print(f"  {model1_name}: {sim2_model1:.4f}")
    print(f"  {model2_name}: {sim2_model2:.4f}")
    
    # 评估语义理解能力
    print("\n🎯 语义理解能力评估:")
    
    # 相似文本应该有更高的相似度
    if sim1_model1 > sim2_model1:
        print(f"  ✅ {model1_name}: 能正确区分相似和不相似文本")
    else:
        print(f"  ❌ {model1_name}: 语义理解可能有问题")
    
    if sim1_model2 > sim2_model2:
        print(f"  ✅ {model2_name}: 能正确区分相似和不相似文本")
    else:
        print(f"  ❌ {model2_name}: 语义理解可能有问题")
    
    # 向量质量对比
    print(f"\n📏 向量质量对比:")
    print(f"  {model1_name} 向量维度: {[len(v) for v in vectors1]}")
    print(f"  {model2_name} 向量维度: {[len(v) for v in vectors2]}")
    
    # 向量范数对比
    norms1 = [np.linalg.norm(v) for v in vectors1]
    norms2 = [np.linalg.norm(v) for v in vectors2]
    
    print(f"  {model1_name} 平均向量范数: {np.mean(norms1):.4f}")
    print(f"  {model2_name} 平均向量范数: {np.mean(norms2):.4f}")

def main():
    """主测试函数"""
    print("🚀 Qwen3-Embedding 原版 vs GGUF 量化版本对比测试")
    print("=" * 60)
    
    # 测试原版模型
    vectors_original, model1_name = test_transformers_original()
    
    # 测试 GGUF 量化版本
    vectors_gguf, model2_name = test_gguf_quantized()
    
    # 对比结果
    if vectors_original and vectors_gguf:
        compare_vectors(vectors_original, vectors_gguf, model1_name, model2_name)
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    main()
