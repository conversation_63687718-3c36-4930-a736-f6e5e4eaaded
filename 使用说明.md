# 舰船航迹行为分类系统 - 使用说明

## 🎯 功能概述

本系统可以根据您输入的**舰船名称**和**12小时航迹坐标**，自动：

1. **获取历史数据**：从在线接口获取该舰船的历史行为航迹
2. **提取有效行为**：筛选出["编队","航渡","停靠","巡航"]四种行为
3. **向量化处理**：将历史航迹和输入航迹都转换为向量
4. **相似度对比**：计算相似度并判断最可能的行为类型
5. **给出解释**：提供详细的分类依据和置信度

## 🚀 快速开始

### 方法1：使用简单API演示

```bash
python simple_api_demo.py
```

这个程序会：
- 运行预设的巡航和停靠示例
- 允许您输入自定义的舰船名称和坐标
- 一键完成整个分类流程

### 方法2：使用交互式分类器

```bash
python ship_behavior_classifier.py
```

这个程序提供完整的交互界面：
- 支持自定义输入
- 多个预设示例
- 详细的结果展示
- 技术细节查看

### 方法3：在代码中调用

```python
# 导入分类函数
from ship_behavior_classifier import classify_ship_behavior

# 准备数据
ship_name = "文森号航空母舰"
trajectory_coords = [
    (35.1234, 139.5678),
    (35.1244, 139.5688),
    (35.1254, 139.5698),
    # ... 更多坐标
]

# 执行分类
result = classify_ship_behavior(ship_name, trajectory_coords)

# 查看结果
if result['success']:
    print(f"预测行为: {result['predicted_action']}")
    print(f"置信度: {result['confidence']:.1%}")
    print(f"解释: {result['explanation']}")
else:
    print(f"分类失败: {result['message']}")
```

## 📋 输入格式

### 舰船名称
- 字符串格式，例如："文森号航空母舰"
- 系统会根据此名称从API获取历史数据

### 航迹坐标
- 格式：`[(纬度, 经度), (纬度, 经度), ...]`
- 示例：`[(35.1234, 139.5678), (35.1244, 139.5688)]`
- 建议：12小时内的坐标点，数量不限

## 📊 输出结果

分类结果包含以下信息：

```python
{
    'success': True,                    # 是否成功
    'predicted_action': '巡航',         # 预测的行为类型
    'confidence': 0.85,                 # 置信度 (0-1)
    'ship_name': '文森号航空母舰',       # 舰船名称
    'explanation': '详细解释...',       # 分类依据和解释
    'detailed_result': {...},           # 详细的技术结果
    'query_data': {...}                 # 查询数据的技术细节
}
```

## 🔧 系统工作原理

### 阶段一：历史数据准备
1. **数据获取**：从API获取舰船历史轨迹
2. **行为筛选**：提取["编队","航渡","停靠","巡航"]行为
3. **事件分块**：将连续相同行为合并为区块
4. **时间窗口**：使用12小时滑动窗口切片
5. **坐标文本化**：转换为Geohash字符串
6. **向量化**：使用Qwen3-Embedding模型生成向量
7. **数据库构建**：存储到FAISS向量数据库

### 阶段二：在线查询
1. **输入处理**：将新航迹转换为相同格式
2. **向量化**：使用相同模型生成查询向量
3. **相似度搜索**：在历史数据库中找到最相似的样本
4. **投票决策**：基于Top-K相似样本进行分类
5. **结果解释**：生成可理解的分类依据

## 🎯 支持的行为类型

- **编队**：多舰协同行动
- **航渡**：长距离航行
- **停靠**：在港口或基地停留
- **巡航**：海上巡逻或警戒

## 📈 示例场景

### 巡航行为示例
```python
# 移动轨迹，坐标逐渐变化
coords = [
    (35.1234, 139.5678),
    (35.1244, 139.5688),
    (35.1254, 139.5698),
    (35.1264, 139.5708)
]
# 预期结果：巡航
```

### 停靠行为示例
```python
# 固定位置，坐标基本不变
coords = [
    (36.8485, -76.2951),  # 诺福克海军基地
    (36.8485, -76.2951),
    (36.8485, -76.2951),
    (36.8485, -76.2951)
]
# 预期结果：停靠
```

## ⚙️ 技术要求

### 必需依赖
- Python 3.7+
- numpy
- requests
- pygeohash
- llama-cpp-python (用于Qwen3-Embedding)
- faiss-cpu (可选，用于高性能搜索)

### 模型文件
- `models/qwen3-embedding-0.6b.gguf` (Qwen3-Embedding模型)

### API配置
- API地址：`http://**************:23071/api/v1/ship/active/trend`
- 需要网络连接以获取历史数据

## 🔍 故障排除

### 常见问题

1. **"无法获取历史数据"**
   - 检查网络连接
   - 确认舰船名称正确
   - 检查API服务是否可用

2. **"向量化失败"**
   - 确认模型文件存在
   - 检查依赖库是否正确安装

3. **"未找到相似样本"**
   - 该舰船可能缺少历史数据
   - 尝试其他已知舰船名称

### 调试模式
在代码中设置详细输出：
```python
# 显示详细处理过程
result = classify_ship_behavior(ship_name, coords)
print(result['detailed_result'])  # 查看技术细节
```

## 📞 技术支持

如有问题，请检查：
1. 依赖库是否完整安装
2. 模型文件是否存在
3. 网络连接是否正常
4. 输入格式是否正确

---

🎉 **现在您可以开始使用舰船航迹行为分类系统了！**
