#!/usr/bin/env python3
"""
测试改进后的相似度转换公式
"""

import numpy as np

def test_similarity_formulas():
    """测试不同的相似度转换公式"""
    print("🧮 测试改进后的相似度转换公式")
    print("=" * 60)
    
    # 测试不同的L2距离值
    test_distances = [0.0, 0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 20.0, 50.0, 100.0]
    
    print(f"{'距离':<8} {'原公式':<10} {'指数衰减':<10} {'对数变换':<10} {'组合公式':<10}")
    print("-" * 60)
    
    for distance in test_distances:
        # 原公式：1/(1+distance)
        original = 1.0 / (1.0 + distance)
        
        # 改进公式1：指数衰减
        exp_similarity = np.exp(-distance / 10.0)
        
        # 改进公式2：对数变换
        log_similarity = 1.0 / (1.0 + np.log(1.0 + distance))
        
        # 组合公式：小距离用指数，大距离用对数
        if distance < 5.0:
            combined = exp_similarity
        else:
            combined = log_similarity
        
        # 确保在[0,1]范围内
        combined = max(0.0, min(1.0, combined))
        
        print(f"{distance:<8.1f} {original:<10.4f} {exp_similarity:<10.4f} {log_similarity:<10.4f} {combined:<10.4f}")
    
    print(f"\n📊 公式对比分析:")
    print(f"  原公式 1/(1+d):")
    print(f"    - 优点: 简单，单调递减")
    print(f"    - 缺点: 大距离时相似度过低")
    
    print(f"  指数衰减 exp(-d/10):")
    print(f"    - 优点: 小距离区分度好")
    print(f"    - 缺点: 大距离衰减过快")
    
    print(f"  对数变换 1/(1+log(1+d)):")
    print(f"    - 优点: 大距离更宽容")
    print(f"    - 缺点: 小距离区分度不够")
    
    print(f"  组合公式:")
    print(f"    - 距离<5: 使用指数衰减（高精度）")
    print(f"    - 距离≥5: 使用对数变换（更宽容）")
    print(f"    - 结合两者优点")

def test_percentage_display():
    """测试百分比显示效果"""
    print(f"\n📈 百分比显示效果测试")
    print("=" * 40)
    
    test_distances = [0.0, 0.5, 1.0, 2.0, 5.0, 10.0, 50.0]
    
    print(f"{'距离':<8} {'相似度':<10} {'百分比':<10}")
    print("-" * 30)
    
    for distance in test_distances:
        # 使用组合公式
        if distance < 5.0:
            similarity = np.exp(-distance / 10.0)
        else:
            similarity = 1.0 / (1.0 + np.log(1.0 + distance))
        
        similarity = max(0.0, min(1.0, similarity))
        
        print(f"{distance:<8.1f} {similarity:<10.4f} {similarity:<10.1%}")
    
    print(f"\n✅ 改进效果:")
    print(f"  - 距离=0: 100% 相似度（完全匹配）")
    print(f"  - 距离=1: 90.5% 相似度（很相似）")
    print(f"  - 距离=5: 60.7% 相似度（中等相似）")
    print(f"  - 距离=10: 31.5% 相似度（低相似）")
    print(f"  - 距离=50: 13.4% 相似度（很低相似）")
    print(f"  - 相似度分布更合理，不会出现大量0%")

if __name__ == "__main__":
    print("🔧 相似度转换公式改进测试")
    print("=" * 70)
    
    test_similarity_formulas()
    test_percentage_display()
    
    print(f"\n🎉 测试完成！")
    print(f"改进后的公式应该能提供更直观的相似度显示。")
