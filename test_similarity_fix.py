#!/usr/bin/env python3
"""
测试相似度计算修复
"""

import sys
import os
import importlib.util

# 导入主程序
spec = importlib.util.spec_from_file_location("behavior_recognition", "src/behavior recognition.py")
behavior_recognition = importlib.util.module_from_spec(spec)
spec.loader.exec_module(behavior_recognition)

def test_similarity_fix():
    """测试相似度计算修复"""
    print("🧪 测试相似度计算修复")
    print("=" * 50)
    
    # 设置向量化模型
    behavior_recognition.set_embedding_model_type("qwen")
    
    # 测试数据
    ship_name = "文森号航空母舰"
    
    # 巡航模式测试
    cruise_coords = [
        (35.1234, 139.5678),
        (35.1244, 139.5688),
        (35.1254, 139.5698)
    ]
    
    print(f"\n📋 测试1：巡航模式")
    print(f"舰船: {ship_name}")
    print(f"坐标: {cruise_coords}")
    
    try:
        result = behavior_recognition.online_trajectory_classification(
            ship_name, cruise_coords, top_k=3
        )
        
        if result['success']:
            print(f"\n✅ 分类成功:")
            print(f"  预测行为: {result['predicted_action']}")
            print(f"  置信度: {result['confidence']:.4f} ({result['confidence']:.1%})")
            
            # 检查数值范围
            if 0 <= result['confidence'] <= 1:
                print(f"  ✅ 置信度数值正常 (0-1范围)")
            else:
                print(f"  ❌ 置信度数值异常: {result['confidence']}")
            
            # 检查相似度样本
            detailed_result = result['detailed_result']
            if 'similar_samples' in detailed_result:
                print(f"\n📊 相似度样本检查:")
                for i, sample in enumerate(detailed_result['similar_samples'][:3], 1):
                    similarity = sample['similarity']
                    print(f"  样本{i}: {sample['action']}, 相似度: {similarity:.4f} ({similarity:.1%})")
                    
                    if 0 <= similarity <= 1:
                        print(f"    ✅ 相似度数值正常")
                    else:
                        print(f"    ❌ 相似度数值异常: {similarity}")
        else:
            print(f"❌ 分类失败: {result['message']}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_distance_to_similarity_conversion():
    """测试距离到相似度的转换"""
    print(f"\n🧮 测试距离到相似度转换公式")
    print("=" * 40)
    
    # 测试不同的L2距离值
    test_distances = [0.0, 0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 100.0]
    
    print(f"L2距离 -> 相似度 (使用公式: 1/(1+distance))")
    print(f"{'距离':<8} {'相似度':<10} {'百分比':<10}")
    print("-" * 30)
    
    for distance in test_distances:
        similarity = 1.0 / (1.0 + distance)
        print(f"{distance:<8.1f} {similarity:<10.4f} {similarity:<10.1%}")
    
    print(f"\n💡 说明:")
    print(f"  - 距离=0 -> 相似度=100% (完全相同)")
    print(f"  - 距离=1 -> 相似度=50%")
    print(f"  - 距离=9 -> 相似度=10%")
    print(f"  - 距离越大，相似度越小")

def main():
    """主函数"""
    print("🔧 相似度计算修复测试程序")
    print("=" * 60)
    
    # 测试距离转换公式
    test_distance_to_similarity_conversion()
    
    # 测试实际分类
    test_similarity_fix()
    
    print(f"\n🎉 测试完成！")
    print(f"\n📝 修复说明:")
    print(f"  1. FAISS返回的是L2距离（越小越相似）")
    print(f"  2. 使用公式 1/(1+distance) 转换为相似度（0-1范围）")
    print(f"  3. 现在相似度和置信度都在正常范围内")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试被中断")
    except Exception as e:
        print(f"❌ 测试程序出错: {e}")
        import traceback
        traceback.print_exc()
