#!/usr/bin/env python3
"""
舰船航迹行为分类器 - 简单易用的接口
输入：舰船名称 + 12小时航迹坐标
输出：预测的行为类型 + 详细解释

使用方法：
1. 直接运行此文件进行交互式分类
2. 或者导入 classify_ship_behavior 函数在其他程序中使用
"""

import sys
import os
import importlib.util
from typing import List, Tuple, Dict

# 导入主程序的函数
spec = importlib.util.spec_from_file_location("behavior_recognition", "src/behavior recognition.py")
behavior_recognition = importlib.util.module_from_spec(spec)
spec.loader.exec_module(behavior_recognition)

def classify_ship_behavior(ship_name: str, trajectory_coords: List[Tuple[float, float]], top_k: int = 5) -> Dict:
    """
    舰船航迹行为分类主接口
    
    Args:
        ship_name: 舰船名称，例如 "文森号航空母舰"
        trajectory_coords: 12小时航迹坐标列表，格式为 [(纬度, 经度), ...]
        top_k: 返回最相似的前k个历史样本用于分类决策
    
    Returns:
        分类结果字典，包含：
        - success: 是否成功
        - predicted_action: 预测的行为类型
        - confidence: 置信度 (0-1)
        - explanation: 详细解释
        - message: 错误信息（如果失败）
    
    示例：
        >>> coords = [(35.1234, 139.5678), (35.1244, 139.5688), ...]
        >>> result = classify_ship_behavior("文森号航空母舰", coords)
        >>> print(f"预测行为: {result['predicted_action']}")
        >>> print(f"置信度: {result['confidence']:.1%}")
    """
    
    print(f"🚀 开始分析 {ship_name} 的航迹行为...")
    print(f"📍 输入坐标点数: {len(trajectory_coords)}")
    
    # 设置向量化模型
    behavior_recognition.set_embedding_model_type("qwen")
    
    # 调用核心分类函数
    result = behavior_recognition.online_trajectory_classification(
        ship_name=ship_name,
        trajectory_coords=trajectory_coords,
        top_k=top_k
    )
    
    return result

def interactive_classification():
    """交互式航迹行为分类"""
    print("🎯 舰船航迹行为分类器")
    print("=" * 50)
    print("请输入舰船信息和航迹坐标进行行为分类")
    print()
    
    while True:
        print("\n📋 请选择操作:")
        print("1. 输入新的航迹进行分类")
        print("2. 使用预设示例进行测试")
        print("3. 退出程序")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            # 用户自定义输入
            ship_name = input("\n请输入舰船名称: ").strip()
            if not ship_name:
                print("❌ 舰船名称不能为空")
                continue
            
            print("\n请输入12小时航迹坐标 (格式: 纬度,经度)")
            print("每行一个坐标点，输入空行结束:")
            
            trajectory_coords = []
            while True:
                coord_input = input("坐标点: ").strip()
                if not coord_input:
                    break
                
                try:
                    parts = coord_input.split(',')
                    if len(parts) != 2:
                        print("❌ 格式错误，请使用 '纬度,经度' 格式")
                        continue
                    
                    lat = float(parts[0].strip())
                    lon = float(parts[1].strip())
                    trajectory_coords.append((lat, lon))
                    print(f"✅ 已添加坐标: ({lat}, {lon})")
                    
                except ValueError:
                    print("❌ 坐标格式错误，请输入数字")
                    continue
            
            if len(trajectory_coords) == 0:
                print("❌ 至少需要输入一个坐标点")
                continue
            
            # 执行分类
            result = classify_ship_behavior(ship_name, trajectory_coords)
            display_result(result)
            
        elif choice == "2":
            # 预设示例测试
            run_preset_examples()
            
        elif choice == "3":
            print("👋 感谢使用舰船航迹行为分类器！")
            break
            
        else:
            print("❌ 无效选择，请重新输入")

def run_preset_examples():
    """运行预设示例"""
    print("\n🧪 预设示例测试")
    print("=" * 40)
    
    examples = [
        {
            "name": "巡航模式示例",
            "ship_name": "文森号航空母舰",
            "description": "移动轨迹，预期行为：巡航",
            "coords": [
                (35.1234, 139.5678),
                (35.1244, 139.5688),
                (35.1254, 139.5698),
                (35.1264, 139.5708),
                (35.1274, 139.5718),
                (35.1284, 139.5728),
                (35.1294, 139.5738),
                (35.1304, 139.5748)
            ]
        },
        {
            "name": "停靠模式示例",
            "ship_name": "文森号航空母舰", 
            "description": "固定位置，预期行为：停靠",
            "coords": [
                (36.8485, -76.2951),  # 诺福克海军基地附近
                (36.8485, -76.2951),
                (36.8485, -76.2951),
                (36.8485, -76.2951),
                (36.8485, -76.2951),
                (36.8485, -76.2951)
            ]
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n📋 示例 {i}: {example['name']}")
        print(f"舰船: {example['ship_name']}")
        print(f"描述: {example['description']}")
        print(f"坐标点数: {len(example['coords'])}")
        
        # 询问是否运行此示例
        run_example = input(f"是否运行此示例？(y/n): ").strip().lower()
        if run_example in ['y', 'yes', '是']:
            result = classify_ship_behavior(example['ship_name'], example['coords'])
            display_result(result)
        
        print("-" * 40)

def display_result(result: Dict):
    """显示分类结果"""
    print("\n" + "="*60)
    print("📊 航迹行为分类结果")
    print("="*60)
    
    if result['success']:
        print(f"🎯 预测行为: {result['predicted_action']}")
        print(f"📈 置信度: {result['confidence']:.1%}")
        print(f"🚢 舰船名称: {result['ship_name']}")
        
        print(f"\n📝 详细解释:")
        print(result['explanation'])
        
        # 显示技术细节（可选）
        show_details = input(f"\n是否显示技术细节？(y/n): ").strip().lower()
        if show_details in ['y', 'yes', '是']:
            query_data = result.get('query_data', {})
            print(f"\n🔧 技术细节:")
            print(f"  - 输入坐标点数: {query_data.get('coordinate_count', 'Unknown')}")
            print(f"  - 向量维度: {len(query_data.get('query_vector', []))}")
            print(f"  - 身份文本: {query_data.get('identity_text', 'Unknown')[:100]}...")
            
    else:
        print(f"❌ 分类失败")
        print(f"错误信息: {result.get('message', '未知错误')}")
    
    print("="*60)

def batch_classify_from_file(file_path: str):
    """从文件批量分类（扩展功能）"""
    # 这里可以实现从CSV或JSON文件读取多个航迹进行批量分类
    # 留作后续扩展
    pass

if __name__ == "__main__":
    print("🌟 舰船航迹行为分类器启动")
    print("基于深度学习的航迹行为识别系统")
    print("支持：编队、航渡、停靠、巡航 四种行为类型")
    print()
    
    try:
        interactive_classification()
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断，再见！")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
