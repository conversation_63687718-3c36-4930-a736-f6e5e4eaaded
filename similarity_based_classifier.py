#!/usr/bin/env python3
"""
基于相似度匹配的航迹行为识别
根据历史行为数据进行相似度匹配来判断新航迹的行为类型
"""

import numpy as np
import sys
import os
import importlib.util
from collections import defaultdict
import json
from datetime import datetime

# 导入阶段一的函数
spec = importlib.util.spec_from_file_location("behavior_recognition", "src/behavior recognition.py")
behavior_recognition = importlib.util.module_from_spec(spec)
spec.loader.exec_module(behavior_recognition)

class SimilarityBasedClassifier:
    """基于相似度的航迹行为分类器"""
    
    def __init__(self):
        self.historical_data = defaultdict(list)  # 存储历史行为数据
        self.ship_name = None
        
    def build_historical_database(self, ship_name, start_date="1990-01-01", end_date="2025-08-03"):
        """
        构建历史行为数据库
        Args:
            ship_name: 舰船名称
            start_date: 开始日期
            end_date: 结束日期
        """
        print(f"🔄 构建 {ship_name} 的历史行为数据库...")
        
        self.ship_name = ship_name
        
        # 设置向量化模型
        behavior_recognition.set_embedding_model_type("qwen")
        
        # 获取历史数据
        filtered_data = behavior_recognition.fetch_ship_data(ship_name, start_date, end_date)
        blocks = behavior_recognition.segment_by_action(filtered_data)
        
        # 生成所有历史时间窗口
        all_windows = []
        valid_blocks = [block for block in blocks if block['size'] > 1]
        
        print(f"处理 {len(valid_blocks)} 个历史行为区块...")
        
        for i, block in enumerate(valid_blocks, 1):
            print(f"  处理区块 {i}/{len(valid_blocks)}: {block['action']}")
            windows = behavior_recognition.create_time_windows(
                block, ship_name, 
                window_hours=12, stride_hours=12, 
                geohash_precision=7, enable_vectorization=True
            )
            all_windows.extend(windows)
        
        # 按行为类型分组存储
        for window in all_windows:
            if window['vector'] is not None:
                action = window['action']
                self.historical_data[action].append({
                    'vector': window['vector'],
                    'identity_text': window['identity_text'],
                    'geohash_sentence': window['geohash_sentence'],
                    'coordinate_count': window['coordinate_count'],
                    'time_range': window.get('time_range', 'Unknown')
                })
        
        # 统计信息
        print(f"\n✅ 历史数据库构建完成:")
        total_samples = 0
        for action, samples in self.historical_data.items():
            count = len(samples)
            total_samples += count
            print(f"  {action}: {count} 个历史样本")
        print(f"  总计: {total_samples} 个历史样本")
        
        return total_samples
    
    def cosine_similarity(self, vec1, vec2):
        """计算余弦相似度"""
        # 处理不同维度的向量
        min_dim = min(len(vec1), len(vec2))
        v1 = vec1[:min_dim] if len(vec1) > min_dim else vec1
        v2 = vec2[:min_dim] if len(vec2) > min_dim else vec2
        
        # 计算余弦相似度
        dot_product = np.dot(v1, v2)
        norm1 = np.linalg.norm(v1)
        norm2 = np.linalg.norm(v2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    def classify_new_trajectory(self, new_trajectory_data, top_k=5, similarity_threshold=0.7):
        """
        对新的航迹数据进行行为分类
        Args:
            new_trajectory_data: 新的航迹数据（12小时窗口）
            top_k: 返回最相似的前k个结果
            similarity_threshold: 相似度阈值
        Returns:
            分类结果和详细信息
        """
        print(f"\n🔍 对新航迹进行行为分类...")
        
        if not self.historical_data:
            raise ValueError("历史数据库为空，请先调用 build_historical_database()")
        
        # 为新数据生成向量
        print("生成新航迹的向量表示...")
        
        # 这里假设 new_trajectory_data 是一个包含坐标和时间的数据结构
        # 需要转换为与历史数据相同的格式
        new_window = self._process_new_trajectory(new_trajectory_data)
        
        if new_window['vector'] is None:
            raise ValueError("无法为新航迹生成向量")
        
        new_vector = new_window['vector']
        
        # 计算与所有历史样本的相似度
        similarities = []
        
        for action, historical_samples in self.historical_data.items():
            for sample in historical_samples:
                similarity = self.cosine_similarity(new_vector, sample['vector'])
                
                similarities.append({
                    'action': action,
                    'similarity': similarity,
                    'historical_sample': sample
                })
        
        # 按相似度排序
        similarities.sort(key=lambda x: x['similarity'], reverse=True)
        
        # 获取top-k结果
        top_similarities = similarities[:top_k]
        
        # 统计各行为类型的相似度
        action_scores = defaultdict(list)
        for sim in top_similarities:
            if sim['similarity'] >= similarity_threshold:
                action_scores[sim['action']].append(sim['similarity'])
        
        # 计算每种行为的平均相似度
        action_avg_scores = {}
        for action, scores in action_scores.items():
            action_avg_scores[action] = {
                'avg_similarity': np.mean(scores),
                'max_similarity': np.max(scores),
                'count': len(scores),
                'scores': scores
            }
        
        # 确定最可能的行为
        if action_avg_scores:
            best_action = max(action_avg_scores.keys(), 
                            key=lambda x: action_avg_scores[x]['avg_similarity'])
            confidence = action_avg_scores[best_action]['avg_similarity']
        else:
            best_action = "未知"
            confidence = 0.0
        
        # 准备结果
        result = {
            'predicted_action': best_action,
            'confidence': confidence,
            'new_trajectory_info': {
                'identity_text': new_window['identity_text'],
                'geohash_sentence': new_window['geohash_sentence'],
                'coordinate_count': new_window['coordinate_count']
            },
            'top_similarities': top_similarities,
            'action_scores': action_avg_scores,
            'similarity_threshold': similarity_threshold
        }
        
        return result
    
    def _process_new_trajectory(self, trajectory_data):
        """处理新的航迹数据，生成向量"""
        # 这里需要根据实际的数据格式来实现
        # 假设 trajectory_data 包含坐标列表和时间信息
        
        # 示例实现：创建一个模拟的时间窗口
        # 实际使用时需要根据您的数据格式调整
        
        if isinstance(trajectory_data, dict) and 'coordinates' in trajectory_data:
            # 如果是字典格式，包含坐标列表
            coordinates = trajectory_data['coordinates']
            
            # 转换为 Geohash
            geohash_list = []
            for coord in coordinates:
                if len(coord) >= 2:
                    lat, lon = coord[0], coord[1]
                    geohash = behavior_recognition.coordinates_to_geohash(lat, lon, precision=7)
                    geohash_list.append(geohash)
            
            geohash_sentence = ' '.join(geohash_list)
            
            # 创建身份文本
            identity_text = behavior_recognition.create_identity_text(self.ship_name, geohash_sentence)
            
            # 生成向量
            vector = behavior_recognition.text_to_vector(identity_text)
            
            return {
                'vector': vector,
                'identity_text': identity_text,
                'geohash_sentence': geohash_sentence,
                'coordinate_count': len(coordinates),
                'time_range': trajectory_data.get('time_range', 'Unknown')
            }
        else:
            raise ValueError("不支持的航迹数据格式")
    
    def print_classification_result(self, result):
        """打印分类结果"""
        print(f"\n📊 航迹行为分类结果:")
        print(f"=" * 50)
        
        print(f"🎯 预测行为: {result['predicted_action']}")
        print(f"🔥 置信度: {result['confidence']:.4f}")
        
        print(f"\n📍 新航迹信息:")
        info = result['new_trajectory_info']
        print(f"  身份文本: {info['identity_text']}")
        print(f"  坐标点数: {info['coordinate_count']}")
        
        print(f"\n📈 各行为类型得分:")
        for action, scores in result['action_scores'].items():
            print(f"  {action}:")
            print(f"    平均相似度: {scores['avg_similarity']:.4f}")
            print(f"    最高相似度: {scores['max_similarity']:.4f}")
            print(f"    匹配样本数: {scores['count']}")
        
        print(f"\n🔍 最相似的历史样本 (Top 5):")
        for i, sim in enumerate(result['top_similarities'][:5], 1):
            print(f"  {i}. {sim['action']} (相似度: {sim['similarity']:.4f})")
    
    def save_historical_database(self, filename=None):
        """保存历史数据库"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"historical_database_{self.ship_name}_{timestamp}.json"
        
        # 转换numpy数组为列表以便JSON序列化
        serializable_data = {}
        for action, samples in self.historical_data.items():
            serializable_data[action] = []
            for sample in samples:
                serializable_sample = sample.copy()
                serializable_sample['vector'] = sample['vector'].tolist()
                serializable_data[action].append(serializable_sample)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                'ship_name': self.ship_name,
                'data': serializable_data,
                'created_at': datetime.now().isoformat()
            }, f, ensure_ascii=False, indent=2)
        
        print(f"💾 历史数据库已保存: {filename}")
        return filename

def demo_similarity_classification():
    """演示相似度分类"""
    print("🚀 基于相似度的航迹行为识别演示")
    print("=" * 60)
    
    # 创建分类器
    classifier = SimilarityBasedClassifier()
    
    # 构建历史数据库
    ship_name = "文森号航空母舰"
    classifier.build_historical_database(ship_name)
    
    # 保存历史数据库
    classifier.save_historical_database()
    
    # 模拟新的航迹数据
    print(f"\n🆕 模拟新航迹数据进行分类...")
    
    # 示例：模拟一个新的航迹（实际使用时替换为真实数据）
    new_trajectory = {
        'coordinates': [
            [35.1234, 139.5678],  # 示例坐标
            [35.1244, 139.5688],
            [35.1254, 139.5698],
            [35.1264, 139.5708],
            [35.1274, 139.5718]
        ],
        'time_range': '2025-08-05 12:00:00 - 2025-08-06 00:00:00'
    }
    
    try:
        # 进行分类
        result = classifier.classify_new_trajectory(new_trajectory)
        
        # 打印结果
        classifier.print_classification_result(result)
        
        print(f"\n🎉 相似度分类演示完成！")
        
    except Exception as e:
        print(f"❌ 分类失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    demo_similarity_classification()
