#!/usr/bin/env python3
"""
测试 Qwen3-Embedding GGUF 量化模型
"""

import os
import sys
import numpy as np

def test_llama_cpp_installation():
    """测试 llama-cpp-python 安装"""
    try:
        from llama_cpp import Llama
        print("✅ llama-cpp-python 已安装")
        return True
    except ImportError:
        print("❌ llama-cpp-python 未安装")
        print("请运行: pip install llama-cpp-python")
        return False

def test_gguf_model_loading():
    """测试 GGUF 模型加载"""
    model_path = "models/qwen3-embedding-0.6b.gguf"
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        print("请先运行 setup_qwen_gguf.py 下载模型")
        return None
    
    try:
        from llama_cpp import Llama
        
        print(f"🔄 正在加载 GGUF 模型: {model_path}")
        
        # 加载模型（嵌入模式）
        llama_model = Llama(
            model_path=model_path,
            embedding=True,  # 启用嵌入模式
            verbose=False,   # 减少输出
            n_ctx=512,       # 上下文长度
            n_batch=1        # 批处理大小
        )
        
        print("✅ GGUF 模型加载成功")
        return llama_model
        
    except Exception as e:
        print(f"❌ GGUF 模型加载失败: {e}")
        return None

def test_embedding_generation(model):
    """测试嵌入向量生成"""
    if model is None:
        return False
    
    try:
        # 测试文本
        test_texts = [
            "文森号航空母舰 xgzbrzp",
            "文森号航空母舰 xgzupup", 
            "文森号航空母舰"
        ]
        
        print("\n🧪 测试嵌入向量生成:")
        
        for i, text in enumerate(test_texts, 1):
            print(f"\n测试 {i}: {text}")
            
            # 生成嵌入向量
            embedding = model.create_embedding(text)
            vector = np.array(embedding['data'][0]['embedding'])
            
            print(f"  ✅ 向量维度: {len(vector)}")
            print(f"  ✅ 向量范数: {np.linalg.norm(vector):.4f}")
            print(f"  ✅ 向量前5个值: {vector[:5].tolist()}")
            
            # 验证向量质量
            if len(vector) > 0 and not np.isnan(vector).any():
                print(f"  ✅ 向量质量: 正常")
            else:
                print(f"  ❌ 向量质量: 异常")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 嵌入向量生成失败: {e}")
        return False

def test_vector_similarity():
    """测试向量相似度"""
    model_path = "models/qwen3-embedding-0.6b.gguf"
    
    if not os.path.exists(model_path):
        print("❌ 跳过相似度测试：模型文件不存在")
        return False
    
    try:
        from llama_cpp import Llama
        
        model = Llama(
            model_path=model_path,
            embedding=True,
            verbose=False
        )
        
        # 测试相似文本
        text1 = "文森号航空母舰 xgzbrzp"
        text2 = "文森号航空母舰 xgzupup"
        text3 = "其他舰船 abc123"
        
        # 生成向量
        vec1 = np.array(model.create_embedding(text1)['data'][0]['embedding'])
        vec2 = np.array(model.create_embedding(text2)['data'][0]['embedding'])
        vec3 = np.array(model.create_embedding(text3)['data'][0]['embedding'])
        
        # 计算余弦相似度
        def cosine_similarity(a, b):
            return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))
        
        sim_1_2 = cosine_similarity(vec1, vec2)
        sim_1_3 = cosine_similarity(vec1, vec3)
        
        print(f"\n🔍 向量相似度测试:")
        print(f"  文本1 vs 文本2 相似度: {sim_1_2:.4f}")
        print(f"  文本1 vs 文本3 相似度: {sim_1_3:.4f}")
        
        # 验证相似度合理性
        if sim_1_2 > sim_1_3:
            print("  ✅ 相似度测试通过：相关文本更相似")
            return True
        else:
            print("  ⚠️ 相似度测试异常：可能需要调整模型")
            return False
            
    except Exception as e:
        print(f"❌ 相似度测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== Qwen3-Embedding GGUF 模型测试 ===\n")
    
    # 1. 测试依赖安装
    if not test_llama_cpp_installation():
        return
    
    # 2. 测试模型加载
    model = test_gguf_model_loading()
    if model is None:
        return
    
    # 3. 测试嵌入生成
    if not test_embedding_generation(model):
        return
    
    # 4. 测试向量相似度
    test_vector_similarity()
    
    print("\n🎉 所有测试完成!")
    print("\n📋 使用说明:")
    print("1. 在 behavior recognition.py 中设置: set_embedding_model_type('qwen')")
    print("2. 确保模型文件路径正确: models/qwen3-embedding-0.6b.gguf")
    print("3. 运行您的航迹行为识别程序")

if __name__ == "__main__":
    main()
