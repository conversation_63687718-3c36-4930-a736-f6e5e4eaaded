#!/usr/bin/env python3
"""
阶段二：机器学习模型训练
基于阶段一生成的向量数据进行行为分类模型训练
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
import os
from datetime import datetime

class BehaviorClassificationTrainer:
    """航迹行为分类模型训练器"""
    
    def __init__(self):
        self.models = {}
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.best_model = None
        self.best_score = 0
        
    def prepare_data_from_windows(self, windows_data):
        """
        从阶段一的窗口数据准备训练数据
        Args:
            windows_data: 阶段一生成的时间窗口数据
        Returns:
            X: 特征矩阵
            y: 标签向量
        """
        print("=== 准备训练数据 ===")
        
        features = []
        labels = []
        
        for window in windows_data:
            if window['vector'] is not None:
                # 使用向量作为特征
                vector = window['vector']
                
                # 确保向量是一维的
                if vector.ndim > 1:
                    vector = vector.flatten()
                
                features.append(vector)
                labels.append(window['action'])
        
        # 处理不同维度的向量
        if features:
            # 找到最大维度
            max_dim = max(len(f) for f in features)
            
            # 填充或截断到统一维度
            normalized_features = []
            for feature in features:
                if len(feature) < max_dim:
                    # 零填充
                    padded = np.zeros(max_dim)
                    padded[:len(feature)] = feature
                    normalized_features.append(padded)
                else:
                    # 截断
                    normalized_features.append(feature[:max_dim])
            
            X = np.array(normalized_features)
            y = np.array(labels)
            
            print(f"✅ 数据准备完成:")
            print(f"  - 样本数量: {len(X)}")
            print(f"  - 特征维度: {X.shape[1]}")
            print(f"  - 行为类别: {np.unique(y)}")
            print(f"  - 各类别样本数:")
            for label in np.unique(y):
                count = np.sum(y == label)
                print(f"    {label}: {count} 个样本")
            
            return X, y
        else:
            raise ValueError("没有找到有效的向量数据")
    
    def split_and_scale_data(self, X, y, test_size=0.2, random_state=42):
        """数据分割和标准化"""
        print(f"\n=== 数据分割和标准化 ===")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state, stratify=y
        )
        
        # 标准化特征
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # 编码标签
        y_train_encoded = self.label_encoder.fit_transform(y_train)
        y_test_encoded = self.label_encoder.transform(y_test)
        
        print(f"✅ 数据分割完成:")
        print(f"  - 训练集: {X_train_scaled.shape[0]} 样本")
        print(f"  - 测试集: {X_test_scaled.shape[0]} 样本")
        print(f"  - 标签映射: {dict(zip(self.label_encoder.classes_, range(len(self.label_encoder.classes_))))}")
        
        return X_train_scaled, X_test_scaled, y_train_encoded, y_test_encoded, y_train, y_test
    
    def train_multiple_models(self, X_train, y_train):
        """训练多种机器学习模型"""
        print(f"\n=== 训练多种模型 ===")
        
        # 定义模型
        models_config = {
            'RandomForest': RandomForestClassifier(n_estimators=100, random_state=42),
            'GradientBoosting': GradientBoostingClassifier(n_estimators=100, random_state=42),
            'SVM': SVC(kernel='rbf', random_state=42),
            'LogisticRegression': LogisticRegression(random_state=42, max_iter=1000)
        }
        
        # 训练每个模型
        for name, model in models_config.items():
            print(f"\n🔄 训练 {name}...")
            
            try:
                # 交叉验证
                cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
                
                # 训练模型
                model.fit(X_train, y_train)
                
                # 保存模型和分数
                self.models[name] = {
                    'model': model,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std()
                }
                
                print(f"  ✅ {name} 训练完成")
                print(f"     交叉验证准确率: {cv_scores.mean():.4f} (±{cv_scores.std():.4f})")
                
                # 更新最佳模型
                if cv_scores.mean() > self.best_score:
                    self.best_score = cv_scores.mean()
                    self.best_model = name
                    
            except Exception as e:
                print(f"  ❌ {name} 训练失败: {e}")
        
        print(f"\n🏆 最佳模型: {self.best_model} (准确率: {self.best_score:.4f})")
    
    def evaluate_models(self, X_test, y_test, y_test_original):
        """评估所有模型"""
        print(f"\n=== 模型评估 ===")
        
        results = {}
        
        for name, model_info in self.models.items():
            model = model_info['model']
            
            # 预测
            y_pred = model.predict(X_test)
            y_pred_original = self.label_encoder.inverse_transform(y_pred)
            
            # 计算指标
            accuracy = accuracy_score(y_test, y_pred)
            
            results[name] = {
                'accuracy': accuracy,
                'predictions': y_pred_original,
                'cv_score': model_info['cv_mean']
            }
            
            print(f"\n📊 {name} 测试结果:")
            print(f"  测试准确率: {accuracy:.4f}")
            print(f"  交叉验证准确率: {model_info['cv_mean']:.4f}")
            
            # 详细分类报告
            print(f"\n  分类报告:")
            print(classification_report(y_test_original, y_pred_original, target_names=self.label_encoder.classes_))
        
        return results
    
    def plot_results(self, results):
        """可视化结果"""
        print(f"\n=== 生成可视化结果 ===")
        
        # 创建结果图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 模型准确率对比
        model_names = list(results.keys())
        test_accuracies = [results[name]['accuracy'] for name in model_names]
        cv_accuracies = [results[name]['cv_score'] for name in model_names]
        
        x = np.arange(len(model_names))
        width = 0.35
        
        axes[0, 0].bar(x - width/2, test_accuracies, width, label='测试准确率', alpha=0.8)
        axes[0, 0].bar(x + width/2, cv_accuracies, width, label='交叉验证准确率', alpha=0.8)
        axes[0, 0].set_xlabel('模型')
        axes[0, 0].set_ylabel('准确率')
        axes[0, 0].set_title('模型性能对比')
        axes[0, 0].set_xticks(x)
        axes[0, 0].set_xticklabels(model_names, rotation=45)
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 最佳模型的混淆矩阵
        best_model = self.models[self.best_model]['model']
        y_pred = best_model.predict(X_test)
        y_pred_original = self.label_encoder.inverse_transform(y_pred)
        
        cm = confusion_matrix(y_test_original, y_pred_original)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=self.label_encoder.classes_,
                   yticklabels=self.label_encoder.classes_,
                   ax=axes[0, 1])
        axes[0, 1].set_title(f'{self.best_model} 混淆矩阵')
        axes[0, 1].set_xlabel('预测标签')
        axes[0, 1].set_ylabel('真实标签')
        
        # 3. 特征重要性（如果支持）
        if hasattr(best_model, 'feature_importances_'):
            importances = best_model.feature_importances_
            # 只显示前20个最重要的特征
            top_indices = np.argsort(importances)[-20:]
            axes[1, 0].barh(range(len(top_indices)), importances[top_indices])
            axes[1, 0].set_xlabel('重要性')
            axes[1, 0].set_ylabel('特征索引')
            axes[1, 0].set_title(f'{self.best_model} 特征重要性 (Top 20)')
        else:
            axes[1, 0].text(0.5, 0.5, f'{self.best_model}\n不支持特征重要性分析', 
                           ha='center', va='center', transform=axes[1, 0].transAxes)
            axes[1, 0].set_title('特征重要性')
        
        # 4. 类别分布
        unique, counts = np.unique(y_test_original, return_counts=True)
        axes[1, 1].pie(counts, labels=unique, autopct='%1.1f%%', startangle=90)
        axes[1, 1].set_title('测试集类别分布')
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        plt.savefig(f'behavior_classification_results_{timestamp}.png', dpi=300, bbox_inches='tight')
        print(f"✅ 结果图表已保存: behavior_classification_results_{timestamp}.png")
        
        plt.show()
    
    def save_best_model(self, model_name=None):
        """保存最佳模型"""
        if not model_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            model_name = f"best_behavior_model_{timestamp}"
        
        # 创建模型目录
        os.makedirs('models', exist_ok=True)
        
        # 保存模型
        model_path = f'models/{model_name}.joblib'
        scaler_path = f'models/{model_name}_scaler.joblib'
        encoder_path = f'models/{model_name}_encoder.joblib'
        
        joblib.dump(self.models[self.best_model]['model'], model_path)
        joblib.dump(self.scaler, scaler_path)
        joblib.dump(self.label_encoder, encoder_path)
        
        print(f"\n💾 最佳模型已保存:")
        print(f"  - 模型: {model_path}")
        print(f"  - 标准化器: {scaler_path}")
        print(f"  - 标签编码器: {encoder_path}")
        
        return model_path

def main():
    """主函数 - 演示如何使用"""
    print("🚀 阶段二：机器学习模型训练")
    print("=" * 50)
    
    # 注意：这里需要从阶段一获取数据
    print("⚠️ 请先运行阶段一生成向量数据")
    print("然后调用 trainer.prepare_data_from_windows(windows_data)")

if __name__ == "__main__":
    main()
