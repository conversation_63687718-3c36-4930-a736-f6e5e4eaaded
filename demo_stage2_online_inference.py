#!/usr/bin/env python3
"""
阶段二：在线查询演示
使用已构建的向量数据库进行实时航迹行为分类
"""

import sys
import os
import importlib.util
import numpy as np

# 导入主程序的函数
spec = importlib.util.spec_from_file_location("behavior_recognition", "src/behavior recognition.py")
behavior_recognition = importlib.util.module_from_spec(spec)
spec.loader.exec_module(behavior_recognition)

def demo_online_inference_simple():
    """简化的在线推理演示"""
    print(f"🚀 阶段二：在线查询演示")
    print(f"=" * 60)
    
    # 设置向量化模型
    behavior_recognition.set_embedding_model_type("qwen")
    
    # 获取在线推理引擎
    engine = behavior_recognition.get_online_inference_engine()
    
    print(f"\n📋 示例1：已知舰船的新航迹分类")
    
    # 示例1：文森号航空母舰的新航迹
    ship_name = "文森号航空母舰"
    
    # 模拟12小时的航迹数据 - 巡航模式（移动轨迹）
    trajectory_coords_cruise = [
        (35.1234, 139.5678),
        (35.1244, 139.5688),
        (35.1254, 139.5698),
        (35.1264, 139.5708),
        (35.1274, 139.5718),
        (35.1284, 139.5728),
        (35.1294, 139.5738),
        (35.1304, 139.5748)
    ]
    
    print(f"\n🔍 测试巡航模式航迹:")
    print(f"  舰船: {ship_name}")
    print(f"  坐标点数: {len(trajectory_coords_cruise)}")
    print(f"  模式: 移动轨迹（预期：巡航）")
    
    try:
        # 处理新航迹数据
        query_data = engine.process_new_trajectory(ship_name, trajectory_coords_cruise)
        
        print(f"\n✅ 新航迹处理完成:")
        print(f"  身份文本: {query_data['identity_text']}")
        print(f"  向量维度: {len(query_data['query_vector'])}")
        
        # 尝试加载已存在的数据库
        vector_db = engine.load_or_build_ship_database(ship_name, force_rebuild=False)
        
        if vector_db:
            print(f"\n🔍 使用已存在的向量数据库进行相似度搜索...")
            
            # 进行相似度搜索和分类
            result = engine.similarity_search_and_classify(query_data, vector_db, top_k=5)
            
            # 生成解释
            explanation = engine.generate_explanation(result)
            
            print(f"\n📊 分类结果:")
            print(explanation)
        else:
            print(f"❌ 无法加载 {ship_name} 的向量数据库")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n" + "="*60)
    print(f"📋 示例2：停靠模式航迹测试")
    
    # 模拟停靠模式的航迹数据（相同位置）
    trajectory_coords_docking = [
        (36.8485, -76.2951),  # 诺福克海军基地附近
        (36.8485, -76.2951),
        (36.8485, -76.2951),
        (36.8485, -76.2951),
        (36.8485, -76.2951),
        (36.8485, -76.2951)
    ]
    
    print(f"\n🔍 测试停靠模式航迹:")
    print(f"  舰船: {ship_name}")
    print(f"  坐标点数: {len(trajectory_coords_docking)}")
    print(f"  模式: 固定位置（预期：停靠）")
    
    try:
        # 处理新航迹数据
        query_data = engine.process_new_trajectory(ship_name, trajectory_coords_docking)
        
        print(f"\n✅ 新航迹处理完成:")
        print(f"  身份文本: {query_data['identity_text']}")
        print(f"  向量维度: {len(query_data['query_vector'])}")
        
        if vector_db:
            # 进行相似度搜索和分类
            result = engine.similarity_search_and_classify(query_data, vector_db, top_k=5)
            
            # 生成解释
            explanation = engine.generate_explanation(result)
            
            print(f"\n📊 分类结果:")
            print(explanation)
        else:
            print(f"❌ 无法使用向量数据库")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_vector_database_loading():
    """测试向量数据库加载"""
    print(f"\n🔧 测试向量数据库加载...")
    
    import glob
    
    # 查找已存在的数据库文件
    db_files = glob.glob("databases/*文森号航空母舰*.db")
    
    if db_files:
        print(f"✅ 找到 {len(db_files)} 个数据库文件:")
        for db_file in db_files:
            print(f"  - {db_file}")
        
        # 尝试加载最新的数据库
        latest_db_file = max(db_files, key=os.path.getctime)
        db_path = latest_db_file[:-3]  # 移除 .db 后缀
        
        try:
            if "faiss" in db_path.lower():
                vector_db = behavior_recognition.FAISSVectorDatabase(db_path)
                if vector_db._load_faiss_index():
                    print(f"✅ 成功加载 FAISS 数据库: {db_path}")
                    
                    # 获取数据库信息
                    db_info = vector_db.get_database_info()
                    print(f"📊 数据库信息:")
                    print(f"  - 总向量数: {db_info.get('total_vectors', 'Unknown')}")
                    print(f"  - 向量维度: {db_info.get('vector_dimension', 'Unknown')}")
                    print(f"  - 索引类型: {db_info.get('index_type', 'Unknown')}")
                    
                    return vector_db
                else:
                    print(f"❌ FAISS 索引加载失败")
            else:
                vector_db = behavior_recognition.VectorDatabase(db_path)
                print(f"✅ 成功加载基础数据库: {db_path}")
                return vector_db
                
        except Exception as e:
            print(f"❌ 数据库加载失败: {e}")
    else:
        print(f"❌ 未找到已存在的数据库文件")
        print(f"💡 请先运行主程序构建向量数据库")
    
    return None

def main():
    """主函数"""
    print(f"🎬 阶段二：在线查询演示程序")
    print(f"=" * 80)
    
    # 测试数据库加载
    vector_db = test_vector_database_loading()
    
    if vector_db:
        # 运行在线推理演示
        demo_online_inference_simple()
    else:
        print(f"\n💡 建议:")
        print(f"1. 先运行主程序构建向量数据库:")
        print(f"   python \"src/behavior recognition.py\"")
        print(f"2. 然后再运行此演示程序")
    
    print(f"\n🎉 演示程序结束")

if __name__ == "__main__":
    main()
